"""
内存管理器模块 - 负责内存使用优化和资源管理
"""
import os
import gc
import threading
import time
import psutil
from typing import Dict, Any, Optional, Callable
from collections import OrderedDict
import weakref


class LRUCache:
    """LRU缓存实现"""
    
    def __init__(self, max_size: int = 100):
        self.max_size = max_size
        self.cache = OrderedDict()
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        with self.lock:
            if key in self.cache:
                # 移动到末尾（最近使用）
                value = self.cache.pop(key)
                self.cache[key] = value
                return value
            return None
    
    def put(self, key: str, value: Any) -> None:
        """添加缓存项"""
        with self.lock:
            if key in self.cache:
                # 更新现有项
                self.cache.pop(key)
            elif len(self.cache) >= self.max_size:
                # 移除最久未使用的项
                oldest_key = next(iter(self.cache))
                self.cache.pop(oldest_key)
            
            self.cache[key] = value
    
    def remove(self, key: str) -> bool:
        """移除缓存项"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
    
    def size(self) -> int:
        """获取缓存大小"""
        with self.lock:
            return len(self.cache)
    
    def keys(self):
        """获取所有键"""
        with self.lock:
            return list(self.cache.keys())


class MemoryManager:
    """内存管理器 - 负责内存使用优化"""
    
    def __init__(self, max_memory_mb: int = 500):
        self.max_memory_mb = max_memory_mb
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        
        # 各种缓存
        self.file_info_cache = LRUCache(max_size=1000)
        self.preview_cache = LRUCache(max_size=20)
        self.thumbnail_cache = LRUCache(max_size=50)
        
        # 内存监控
        self.memory_check_interval = 30  # 秒
        self.memory_monitor_thread = None
        self.monitoring = False
        
        # 回调函数
        self.memory_warning_callback = None
        self.cleanup_callback = None
        
        # 弱引用集合，用于跟踪需要清理的对象
        self.managed_objects = weakref.WeakSet()
        
        # 启动内存监控
        self.start_memory_monitoring()
    
    def start_memory_monitoring(self):
        """启动内存监控"""
        if not self.monitoring:
            self.monitoring = True
            self.memory_monitor_thread = threading.Thread(
                target=self._memory_monitor_loop,
                daemon=True
            )
            self.memory_monitor_thread.start()
            print("内存监控已启动")
    
    def stop_memory_monitoring(self):
        """停止内存监控"""
        self.monitoring = False
        if self.memory_monitor_thread:
            self.memory_monitor_thread.join(timeout=1)
        print("内存监控已停止")
    
    def _memory_monitor_loop(self):
        """内存监控循环"""
        while self.monitoring:
            try:
                current_memory = self.get_current_memory_usage()
                
                if current_memory > self.max_memory_bytes:
                    print(f"内存使用超限: {current_memory / 1024 / 1024:.1f} MB")
                    self._trigger_memory_cleanup()
                
                # 定期清理无效缓存
                self._cleanup_invalid_cache_entries()
                
                time.sleep(self.memory_check_interval)
                
            except Exception as e:
                print(f"内存监控出错: {e}")
                time.sleep(5)
    
    def get_current_memory_usage(self) -> int:
        """获取当前内存使用量（字节）"""
        try:
            process = psutil.Process()
            return process.memory_info().rss
        except Exception:
            return 0
    
    def get_memory_info(self) -> Dict[str, Any]:
        """获取详细内存信息"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                'rss': memory_info.rss,  # 物理内存
                'vms': memory_info.vms,  # 虚拟内存
                'percent': process.memory_percent(),  # 内存使用百分比
                'available': psutil.virtual_memory().available,  # 系统可用内存
                'cache_sizes': {
                    'file_info': self.file_info_cache.size(),
                    'preview': self.preview_cache.size(),
                    'thumbnail': self.thumbnail_cache.size()
                }
            }
        except Exception as e:
            print(f"获取内存信息失败: {e}")
            return {}
    
    def _trigger_memory_cleanup(self):
        """触发内存清理"""
        print("开始内存清理...")
        
        # 清理缓存
        self._cleanup_caches()
        
        # 强制垃圾回收
        self._force_garbage_collection()
        
        # 调用外部清理回调
        if self.cleanup_callback:
            try:
                self.cleanup_callback()
            except Exception as e:
                print(f"外部清理回调失败: {e}")
        
        # 检查清理效果
        new_memory = self.get_current_memory_usage()
        print(f"内存清理完成，当前使用: {new_memory / 1024 / 1024:.1f} MB")
        
        # 如果内存仍然过高，发出警告
        if new_memory > self.max_memory_bytes and self.memory_warning_callback:
            try:
                self.memory_warning_callback(new_memory)
            except Exception as e:
                print(f"内存警告回调失败: {e}")
    
    def _cleanup_caches(self):
        """清理缓存"""
        # 清理预览缓存（最占内存）
        preview_size = self.preview_cache.size()
        if preview_size > 10:
            # 只保留最近的5个预览
            keys_to_remove = self.preview_cache.keys()[:-5]
            for key in keys_to_remove:
                self.preview_cache.remove(key)
            print(f"清理了 {len(keys_to_remove)} 个预览缓存")
        
        # 清理缩略图缓存
        thumbnail_size = self.thumbnail_cache.size()
        if thumbnail_size > 30:
            # 只保留最近的20个缩略图
            keys_to_remove = self.thumbnail_cache.keys()[:-20]
            for key in keys_to_remove:
                self.thumbnail_cache.remove(key)
            print(f"清理了 {len(keys_to_remove)} 个缩略图缓存")
        
        # 适度清理文件信息缓存
        file_info_size = self.file_info_cache.size()
        if file_info_size > 800:
            # 只保留最近的500个文件信息
            keys_to_remove = self.file_info_cache.keys()[:-500]
            for key in keys_to_remove:
                self.file_info_cache.remove(key)
            print(f"清理了 {len(keys_to_remove)} 个文件信息缓存")
    
    def _cleanup_invalid_cache_entries(self):
        """清理无效的缓存项"""
        try:
            # 清理不存在文件的缓存
            invalid_keys = []
            
            for key in self.file_info_cache.keys():
                if key.startswith('/') or key.startswith('\\') or ':' in key:
                    # 看起来像文件路径
                    file_path = key.split('_')[0]  # 简单提取文件路径
                    if not os.path.exists(file_path):
                        invalid_keys.append(key)
            
            for key in invalid_keys:
                self.file_info_cache.remove(key)
            
            if invalid_keys:
                print(f"清理了 {len(invalid_keys)} 个无效文件信息缓存")
                
        except Exception as e:
            print(f"清理无效缓存项失败: {e}")
    
    def _force_garbage_collection(self):
        """强制垃圾回收"""
        try:
            # 多次垃圾回收以确保彻底清理
            for i in range(3):
                collected = gc.collect()
                if collected > 0:
                    print(f"垃圾回收第{i+1}轮: 清理了 {collected} 个对象")
                
        except Exception as e:
            print(f"垃圾回收失败: {e}")
    
    def register_cleanup_callback(self, callback: Callable):
        """注册清理回调函数"""
        self.cleanup_callback = callback
    
    def register_memory_warning_callback(self, callback: Callable):
        """注册内存警告回调函数"""
        self.memory_warning_callback = callback
    
    def add_managed_object(self, obj):
        """添加需要管理的对象"""
        self.managed_objects.add(obj)
    
    def cache_file_info(self, file_path: str, info: Dict[str, Any]):
        """缓存文件信息"""
        try:
            # 生成缓存键
            stat = os.stat(file_path)
            cache_key = f"{file_path}_{stat.st_mtime}_{stat.st_size}"
            
            self.file_info_cache.put(cache_key, info)
            
        except Exception as e:
            print(f"缓存文件信息失败: {e}")
    
    def get_cached_file_info(self, file_path: str) -> Optional[Dict[str, Any]]:
        """获取缓存的文件信息"""
        try:
            stat = os.stat(file_path)
            cache_key = f"{file_path}_{stat.st_mtime}_{stat.st_size}"
            
            return self.file_info_cache.get(cache_key)
            
        except Exception:
            return None
    
    def cache_preview(self, file_path: str, preview_data: Any):
        """缓存预览数据"""
        try:
            # 检查预览数据大小（粗略估计）
            if hasattr(preview_data, 'width') and hasattr(preview_data, 'height'):
                # 图片预览
                estimated_size = preview_data.width() * preview_data.height() * 4  # RGBA
                if estimated_size > 10 * 1024 * 1024:  # 超过10MB的预览不缓存
                    print(f"预览过大，不缓存: {file_path}")
                    return
            
            self.preview_cache.put(file_path, preview_data)
            
        except Exception as e:
            print(f"缓存预览失败: {e}")
    
    def get_cached_preview(self, file_path: str) -> Optional[Any]:
        """获取缓存的预览"""
        return self.preview_cache.get(file_path)
    
    def clear_all_caches(self):
        """清空所有缓存"""
        self.file_info_cache.clear()
        self.preview_cache.clear()
        self.thumbnail_cache.clear()
        print("所有缓存已清空")
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'file_info_cache_size': self.file_info_cache.size(),
            'preview_cache_size': self.preview_cache.size(),
            'thumbnail_cache_size': self.thumbnail_cache.size(),
            'managed_objects_count': len(self.managed_objects),
            'memory_info': self.get_memory_info()
        }
    
    def optimize_for_large_dataset(self):
        """为大数据集优化内存设置"""
        # 减少缓存大小
        self.file_info_cache = LRUCache(max_size=500)
        self.preview_cache = LRUCache(max_size=10)
        self.thumbnail_cache = LRUCache(max_size=20)
        
        # 增加内存检查频率
        self.memory_check_interval = 15
        
        print("已切换到大数据集优化模式")
    
    def optimize_for_performance(self):
        """为性能优化内存设置"""
        # 增加缓存大小
        self.file_info_cache = LRUCache(max_size=2000)
        self.preview_cache = LRUCache(max_size=50)
        self.thumbnail_cache = LRUCache(max_size=100)
        
        # 减少内存检查频率
        self.memory_check_interval = 60
        
        print("已切换到性能优化模式")
    
    def __del__(self):
        """析构函数"""
        self.stop_memory_monitoring()
        self.clear_all_caches()
