"""
异步任务管理器模块 - 负责后台任务的管理和执行
"""
import threading
import queue
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Callable, Any, Optional


class AsyncTaskManager:
    """异步任务管理器 - 专门负责后台任务管理"""
    
    def __init__(self, max_workers=4):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.active_tasks = {}
        self.task_counter = 0
        self.task_lock = threading.Lock()
        
        # 任务队列
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        
        # 控制标志
        self.shutdown_flag = False
        
    def submit_task(self, func: Callable, *args, callback: Optional[Callable] = None, 
                   error_callback: Optional[Callable] = None, task_name: str = None, **kwargs) -> int:
        """提交异步任务"""
        with self.task_lock:
            self.task_counter += 1
            task_id = self.task_counter
        
        if task_name is None:
            task_name = f"Task_{task_id}"
        
        # 创建任务信息
        task_info = {
            'id': task_id,
            'name': task_name,
            'start_time': time.time(),
            'status': 'pending',
            'callback': callback,
            'error_callback': error_callback
        }
        
        # 提交任务到线程池
        future = self.executor.submit(self._execute_task, task_info, func, *args, **kwargs)
        
        # 保存任务信息
        self.active_tasks[task_id] = {
            'info': task_info,
            'future': future
        }
        
        return task_id
    
    def _execute_task(self, task_info: dict, func: Callable, *args, **kwargs):
        """执行任务的内部方法"""
        task_id = task_info['id']
        
        try:
            # 更新任务状态
            task_info['status'] = 'running'
            
            # 执行任务
            result = func(*args, **kwargs)
            
            # 更新任务状态
            task_info['status'] = 'completed'
            task_info['end_time'] = time.time()
            task_info['result'] = result
            
            # 调用成功回调
            if task_info['callback']:
                try:
                    task_info['callback'](result)
                except Exception as e:
                    print(f"任务 {task_id} 回调函数执行失败: {e}")
            
            return result
            
        except Exception as e:
            # 更新任务状态
            task_info['status'] = 'failed'
            task_info['end_time'] = time.time()
            task_info['error'] = str(e)
            
            # 调用错误回调
            if task_info['error_callback']:
                try:
                    task_info['error_callback'](e)
                except Exception as callback_error:
                    print(f"任务 {task_id} 错误回调函数执行失败: {callback_error}")
            
            raise e
        
        finally:
            # 清理任务信息
            with self.task_lock:
                if task_id in self.active_tasks:
                    del self.active_tasks[task_id]
    
    def cancel_task(self, task_id: int) -> bool:
        """取消指定任务"""
        with self.task_lock:
            if task_id in self.active_tasks:
                task = self.active_tasks[task_id]
                success = task['future'].cancel()
                if success:
                    task['info']['status'] = 'cancelled'
                    del self.active_tasks[task_id]
                return success
        return False
    
    def cancel_all_tasks(self):
        """取消所有任务"""
        with self.task_lock:
            cancelled_count = 0
            for task_id in list(self.active_tasks.keys()):
                if self.cancel_task(task_id):
                    cancelled_count += 1
            return cancelled_count
    
    def get_task_status(self, task_id: int) -> Optional[dict]:
        """获取任务状态"""
        with self.task_lock:
            if task_id in self.active_tasks:
                return self.active_tasks[task_id]['info'].copy()
        return None
    
    def get_all_tasks_status(self) -> list:
        """获取所有任务状态"""
        with self.task_lock:
            return [task['info'].copy() for task in self.active_tasks.values()]
    
    def wait_for_task(self, task_id: int, timeout: Optional[float] = None) -> Any:
        """等待指定任务完成"""
        with self.task_lock:
            if task_id not in self.active_tasks:
                return None
            
            future = self.active_tasks[task_id]['future']
        
        try:
            return future.result(timeout=timeout)
        except Exception as e:
            print(f"等待任务 {task_id} 完成时出错: {e}")
            return None
    
    def wait_for_all_tasks(self, timeout: Optional[float] = None):
        """等待所有任务完成"""
        with self.task_lock:
            futures = [task['future'] for task in self.active_tasks.values()]
        
        if not futures:
            return
        
        try:
            # 等待所有任务完成
            for future in as_completed(futures, timeout=timeout):
                try:
                    future.result()
                except Exception as e:
                    print(f"任务执行失败: {e}")
        except Exception as e:
            print(f"等待任务完成时出错: {e}")
    
    def is_task_running(self, task_id: int) -> bool:
        """检查任务是否正在运行"""
        status = self.get_task_status(task_id)
        return status and status['status'] == 'running'
    
    def get_active_task_count(self) -> int:
        """获取活跃任务数量"""
        with self.task_lock:
            return len(self.active_tasks)
    
    def shutdown(self, wait: bool = True):
        """关闭任务管理器"""
        self.shutdown_flag = True
        
        if wait:
            # 等待所有任务完成
            self.wait_for_all_tasks()
        else:
            # 取消所有任务
            self.cancel_all_tasks()
        
        # 关闭线程池
        self.executor.shutdown(wait=wait)
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.shutdown()


class ProgressTracker:
    """进度跟踪器 - 用于跟踪长时间运行任务的进度"""
    
    def __init__(self, total: int = 100, callback: Optional[Callable] = None):
        self.total = total
        self.current = 0
        self.callback = callback
        self.start_time = time.time()
        self.lock = threading.Lock()
    
    def update(self, increment: int = 1):
        """更新进度"""
        with self.lock:
            self.current = min(self.current + increment, self.total)
            
            if self.callback:
                progress_info = {
                    'current': self.current,
                    'total': self.total,
                    'percentage': (self.current / self.total) * 100 if self.total > 0 else 0,
                    'elapsed_time': time.time() - self.start_time
                }
                
                try:
                    self.callback(progress_info)
                except Exception as e:
                    print(f"进度回调函数执行失败: {e}")
    
    def set_progress(self, value: int):
        """设置进度值"""
        with self.lock:
            self.current = max(0, min(value, self.total))
            
            if self.callback:
                progress_info = {
                    'current': self.current,
                    'total': self.total,
                    'percentage': (self.current / self.total) * 100 if self.total > 0 else 0,
                    'elapsed_time': time.time() - self.start_time
                }
                
                try:
                    self.callback(progress_info)
                except Exception as e:
                    print(f"进度回调函数执行失败: {e}")
    
    def reset(self, total: Optional[int] = None):
        """重置进度"""
        with self.lock:
            if total is not None:
                self.total = total
            self.current = 0
            self.start_time = time.time()
    
    def is_complete(self) -> bool:
        """检查是否完成"""
        with self.lock:
            return self.current >= self.total
    
    def get_progress_info(self) -> dict:
        """获取进度信息"""
        with self.lock:
            return {
                'current': self.current,
                'total': self.total,
                'percentage': (self.current / self.total) * 100 if self.total > 0 else 0,
                'elapsed_time': time.time() - self.start_time,
                'is_complete': self.current >= self.total
            }
