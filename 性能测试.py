#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重复文件查找器性能测试 - 专门测试删除文件后的响应速度
"""

import os
import sys
import time
import tempfile
import shutil
import threading
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_large_test_set():
    """创建大量测试文件来模拟真实场景"""
    test_dir = tempfile.mkdtemp(prefix="perf_test_")
    print(f"创建测试目录: {test_dir}")
    
    test_files = []
    
    # 创建多个相同大小的文件组（确保有重复）
    file_sizes = [1024, 2048, 4096, 8192, 16384]  # 1KB, 2KB, 4KB, 8KB, 16KB
    files_per_size = 5  # 每个大小创建5个相同的文件

    for size in file_sizes:
        content = 'x' * size  # 相同内容确保大小相同
        for i in range(files_per_size):
            file_path = os.path.join(test_dir, f"test_{size}b_{i:03d}.txt")
            with open(file_path, 'w') as f:
                f.write(content)
            test_files.append(file_path)
    
    # 创建一些独特大小的文件
    for i in range(10):
        unique_size = 1000 + i * 100
        file_path = os.path.join(test_dir, f"unique_{unique_size}b.txt")
        with open(file_path, 'w') as f:
            f.write('y' * unique_size)
        test_files.append(file_path)
    
    print(f"创建了 {len(test_files)} 个测试文件")
    return test_dir, test_files

def test_delete_performance():
    """测试删除文件的性能"""
    print("=" * 60)
    print("删除文件性能测试")
    print("=" * 60)
    
    # 创建大量测试文件
    test_dir, test_files = create_large_test_set()
    
    try:
        # 导入重复文件查找器
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        # 创建应用实例
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        app = FileSearchApp(root)
        
        # 设置测试目录并执行搜索
        app.current_directory = test_dir
        print(f"\n1. 执行初始扫描...")

        # 启用包含图片文件选项（这样会包含所有文件类型）
        app.include_images_var.set(True)

        # 模拟按大小搜索
        start_time = time.time()
        app._search_by_size_worker()
        scan_time = time.time() - start_time
        print(f"初始扫描耗时: {scan_time:.3f} 秒")
        print(f"找到 {len(app.current_results)} 个重复文件组")
        
        # 等待界面更新完成
        root.update()
        
        print(f"\n2. 测试单个文件删除性能...")
        
        # 选择一个要删除的文件
        first_group = next(iter(app.current_results.values()))
        file_to_delete = first_group[0]
        size_key = None
        for size, files in app.current_results.items():
            if file_to_delete in files:
                size_key = size
                break
        
        print(f"删除文件: {os.path.basename(file_to_delete)}")
        
        # 测试删除性能
        start_time = time.time()
        app.delete_file(file_to_delete, size_key)
        delete_time = time.time() - start_time
        
        print(f"单个文件删除耗时: {delete_time:.3f} 秒")
        
        print(f"\n3. 测试批量删除性能...")
        
        # 选择多个文件进行批量删除
        files_to_delete = []
        for size, files in list(app.current_results.items())[:3]:  # 选择前3组
            if len(files) > 1:
                files_to_delete.extend(files[:2])  # 每组删除2个文件
        
        print(f"批量删除 {len(files_to_delete)} 个文件")
        
        # 模拟勾选文件
        for file_path in files_to_delete:
            if file_path in app.checkbox_vars:
                app.checkbox_vars[file_path].set(True)
        
        # 测试批量删除性能
        start_time = time.time()
        app.delete_selected_files()
        batch_delete_time = time.time() - start_time
        
        print(f"批量删除耗时: {batch_delete_time:.3f} 秒")
        
        print(f"\n4. 测试缓存效果...")
        
        # 检查缓存状态
        print(f"文件信息缓存数量: {len(app._file_info_cache)}")
        print(f"显示信息缓存数量: {len(app._display_info_cache)}")
        print(f"扫描结果缓存: {'有效' if app._scan_results_cache else '无效'}")
        
        # 测试缓存清理
        start_time = time.time()
        app.smart_cache_cleanup()
        cleanup_time = time.time() - start_time
        
        print(f"缓存清理耗时: {cleanup_time:.3f} 秒")
        print(f"清理后文件信息缓存数量: {len(app._file_info_cache)}")
        
        print(f"\n5. 性能总结:")
        print(f"- 初始扫描: {scan_time:.3f} 秒")
        print(f"- 单个删除: {delete_time:.3f} 秒")
        print(f"- 批量删除: {batch_delete_time:.3f} 秒")
        print(f"- 缓存清理: {cleanup_time:.3f} 秒")
        
        # 计算性能提升
        if delete_time < 0.1:
            print("✓ 单个文件删除响应速度优秀 (<0.1秒)")
        elif delete_time < 0.5:
            print("✓ 单个文件删除响应速度良好 (<0.5秒)")
        else:
            print("⚠ 单个文件删除响应速度需要优化 (>0.5秒)")
        
        if batch_delete_time < len(files_to_delete) * 0.1:
            print("✓ 批量删除性能优秀")
        else:
            print("⚠ 批量删除性能需要优化")
        
        root.destroy()
        
    except Exception as e:
        print(f"性能测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"\n清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理测试目录失败: {str(e)}")

def test_cache_hit_rate():
    """测试缓存命中率"""
    print("\n" + "=" * 60)
    print("缓存命中率测试")
    print("=" * 60)
    
    test_dir, test_files = create_large_test_set()
    
    try:
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 第一次获取文件信息（建立缓存）
        print("第一次获取文件信息（建立缓存）...")
        start_time = time.time()
        for file_path in test_files[:50]:  # 测试前50个文件
            app.get_file_size_cached(file_path)
            app.get_file_display_info_cached(file_path)
        first_time = time.time() - start_time
        
        # 第二次获取文件信息（使用缓存）
        print("第二次获取文件信息（使用缓存）...")
        start_time = time.time()
        for file_path in test_files[:50]:
            app.get_file_size_cached(file_path)
            app.get_file_display_info_cached(file_path)
        second_time = time.time() - start_time
        
        print(f"第一次获取耗时: {first_time:.3f} 秒")
        print(f"第二次获取耗时: {second_time:.3f} 秒")
        
        if second_time < first_time:
            improvement = ((first_time - second_time) / first_time) * 100
            print(f"✓ 缓存生效！性能提升: {improvement:.1f}%")
        else:
            print("⚠ 缓存效果不明显")
        
        print(f"文件信息缓存数量: {len(app._file_info_cache)}")
        print(f"显示信息缓存数量: {len(app._display_info_cache)}")
        
        root.destroy()
        
    except Exception as e:
        print(f"缓存测试出错: {str(e)}")
    
    finally:
        try:
            shutil.rmtree(test_dir)
        except:
            pass

def test_memory_efficiency():
    """测试内存使用效率"""
    print("\n" + "=" * 60)
    print("内存使用效率测试")
    print("=" * 60)
    
    try:
        import psutil
        process = psutil.Process()
        
        initial_memory = process.memory_info().rss / 1024 / 1024
        print(f"初始内存使用: {initial_memory:.2f} MB")
        
        # 创建应用并加载大量数据
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 模拟大量缓存数据
        for i in range(5000):
            fake_path = f"/fake/path/file_{i}.txt"
            app.cache_file_info(fake_path, {
                'size': i * 1024,
                'duration': i * 10.5,
                'last_modified': time.time()
            })
            
            cache_key = f"{fake_path}_/fake/dir"
            app._display_info_cache[cache_key] = {
                'display_path': f"file_{i}.txt",
                'file_info': f"file_{i}.txt (1KB)",
                'exists': True,
                'last_modified': time.time()
            }
        
        loaded_memory = process.memory_info().rss / 1024 / 1024
        print(f"加载5000个缓存项后内存使用: {loaded_memory:.2f} MB")
        print(f"内存增长: {loaded_memory - initial_memory:.2f} MB")
        
        # 测试缓存清理
        app._file_info_cache.clear()
        app._display_info_cache.clear()
        
        cleaned_memory = process.memory_info().rss / 1024 / 1024
        print(f"清理缓存后内存使用: {cleaned_memory:.2f} MB")
        
        root.destroy()
        
    except ImportError:
        print("需要安装 psutil 来测试内存使用: pip install psutil")
    except Exception as e:
        print(f"内存测试出错: {str(e)}")

if __name__ == "__main__":
    print("重复文件查找器性能测试")
    print("=" * 60)
    
    test_delete_performance()
    test_cache_hit_rate()
    test_memory_efficiency()
    
    print("\n" + "=" * 60)
    print("性能测试完成")
    print("=" * 60)
    
    print("\n优化建议:")
    print("1. 如果删除响应时间 > 0.5秒，考虑进一步优化UI更新机制")
    print("2. 如果缓存命中率低，检查缓存失效策略")
    print("3. 如果内存使用过高，考虑限制缓存大小")
    print("4. 对于大量文件的场景，考虑使用虚拟化列表显示")
