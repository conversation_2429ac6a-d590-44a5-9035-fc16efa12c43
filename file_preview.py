"""
文件预览模块 - 负责图片和视频预览功能
"""
import os
import threading
import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk
import cv2
import mimetypes


class FilePreviewManager:
    """文件预览管理器 - 专门负责文件预览功能"""
    
    def __init__(self, preview_label, preview_filename_var, stop_preview_btn):
        self.preview_label = preview_label
        self.preview_filename_var = preview_filename_var
        self.stop_preview_btn = stop_preview_btn
        
        # 预览相关状态
        self._preview_cache = {}
        self._current_preview = None
        self._is_processing = False
        self._preview_timer = None
        
        # 视频播放相关
        self.video_capture = None
        self.is_playing = False
        self.video_fps = 30
        self.frame_interval = 33
        self.frame_skip = 1
        
        # 获取预览区域的引用
        self.preview_frame = preview_label.master
        
    def preview_file(self, file_path, root_widget):
        """预览文件"""
        # 防止重复处理
        if self._is_processing or file_path == self._current_preview:
            return
            
        # 检查文件是否存在
        if not os.path.exists(file_path):
            self.preview_label.configure(image='', text="文件不存在")
            return
            
        # 获取文件类型
        mime_type, _ = mimetypes.guess_type(file_path)
        if mime_type is None:
            self.preview_label.configure(image='', text="不支持的文件类型")
            return
            
        # 停止当前播放
        self.stop_video_playback()
        
        # 启用停止按钮并显示文件名
        self.stop_preview_btn.configure(state='normal')
        filename = os.path.basename(file_path)
        self.preview_filename_var.set(filename)
        
        # 检查缓存
        if file_path in self._preview_cache:
            self.preview_label.configure(image=self._preview_cache[file_path])
            self._current_preview = file_path
            return
            
        # 设置处理标志
        self._is_processing = True
        
        # 取消之前的定时器
        if self._preview_timer:
            root_widget.after_cancel(self._preview_timer)
        
        # 创建预览任务
        def load_preview():
            try:
                if not os.path.exists(file_path):
                    self.preview_label.configure(image='', text="文件不存在")
                    return
                    
                if mime_type.startswith('image/'):
                    self._show_image(file_path)
                elif mime_type.startswith('video/'):
                    self._show_video(file_path, root_widget)
                    
                self._current_preview = file_path
                
                # 清理旧缓存
                if len(self._preview_cache) > 10:
                    oldest = next(iter(self._preview_cache))
                    del self._preview_cache[oldest]
                    
            finally:
                self._is_processing = False
                
        # 延迟加载预览
        self._preview_timer = root_widget.after(100, load_preview)
    
    def _show_image(self, image_path):
        """显示图片预览"""
        try:
            with Image.open(image_path) as img:
                # 获取预览区域大小
                preview_width = self.preview_frame.winfo_width() - 20
                preview_height = self.preview_frame.winfo_height() - 20
                
                # 计算缩放比例
                width, height = img.size
                ratio = min(preview_width/width, preview_height/height)
                
                # 调整大小
                if ratio < 1:
                    new_size = (int(width*ratio), int(height*ratio))
                    img = img.resize(new_size, Image.Resampling.LANCZOS)
                
                # 转换并缓存
                photo = ImageTk.PhotoImage(img)
                self._preview_cache[image_path] = photo
                self.preview_label.configure(image=photo)
                
                # 图片预览完成后禁用停止按钮
                self.stop_preview_btn.configure(state='disabled')
                
        except Exception as e:
            self.preview_label.configure(image='', text=f"无法预览图片: {str(e)}")
            self.stop_preview_btn.configure(state='disabled')
    
    def _show_video(self, video_path, root_widget):
        """显示视频预览"""
        if not self.is_playing:
            try:
                def init_video():
                    self.video_capture = cv2.VideoCapture(video_path)
                    if not self.video_capture.isOpened():
                        raise Exception("无法打开视频文件")
                    
                    # 获取视频帧率
                    fps = self.video_capture.get(cv2.CAP_PROP_FPS)
                    if fps > 0 and fps <= 120:
                        self.video_fps = fps
                        
                        # 优化播放参数
                        if fps > 60:
                            self.frame_skip = int(fps / 25)
                            self.frame_interval = 40
                        elif fps > 30:
                            self.frame_skip = 2
                            self.frame_interval = int(2000 / fps)
                        else:
                            self.frame_skip = 1
                            self.frame_interval = max(int(1000 / fps), 25)
                    else:
                        self.video_fps = 25
                        self.frame_interval = 40
                        self.frame_skip = 2
                    
                    self.is_playing = True
                    self._update_video_frame(root_widget)
                
                # 在新线程中初始化
                threading.Thread(target=init_video, daemon=True).start()
                
            except Exception as e:
                self.preview_label.configure(text=f"无法预览视频: {str(e)}")
                self.is_playing = False
                self.stop_preview_btn.configure(state='disabled')
    
    def _update_video_frame(self, root_widget):
        """更新视频帧"""
        if not self.is_playing or self.video_capture is None:
            return
            
        try:
            # 跳帧处理
            frame = None
            ret = False
            for i in range(self.frame_skip):
                ret, frame = self.video_capture.read()
                if not ret:
                    break
            
            if ret:
                # 获取预览区域大小
                preview_width = self.preview_frame.winfo_width() - 20
                preview_height = self.preview_frame.winfo_height() - 20
                
                if preview_width < 100 or preview_height < 100:
                    if self.is_playing:
                        root_widget.after(self.frame_interval, lambda: self._update_video_frame(root_widget))
                    return
                
                # 限制最大预览尺寸
                max_preview_size = 400
                preview_width = min(preview_width, max_preview_size)
                preview_height = min(preview_height, max_preview_size)
                
                # 缩放处理
                height, width = frame.shape[:2]
                ratio = min(preview_width/width, preview_height/height)
                
                if ratio < 0.5:
                    # 先快速缩放
                    temp_size = (int(width * 0.5), int(height * 0.5))
                    frame = cv2.resize(frame, temp_size, interpolation=cv2.INTER_AREA)
                    height, width = frame.shape[:2]
                    ratio = min(preview_width/width, preview_height/height)
                
                if ratio < 1.0:
                    new_size = (int(width*ratio), int(height*ratio))
                    frame = cv2.resize(frame, new_size, interpolation=cv2.INTER_LINEAR)
                
                # 转换颜色空间并显示
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                photo = ImageTk.PhotoImage(Image.fromarray(frame_rgb))
                
                self.preview_label.configure(image=photo)
                self.preview_label.image = photo
                
                # 继续播放
                if self.is_playing:
                    root_widget.after(self.frame_interval, lambda: self._update_video_frame(root_widget))
            else:
                # 重新开始播放
                self.video_capture.set(cv2.CAP_PROP_POS_FRAMES, 0)
                if self.is_playing:
                    root_widget.after(self.frame_interval, lambda: self._update_video_frame(root_widget))
                    
        except Exception as e:
            print(f"视频播放出错: {e}")
            self.is_playing = False
            self.stop_preview_btn.configure(state='disabled')
    
    def stop_video_playback(self):
        """停止视频播放"""
        self.is_playing = False
        if self.video_capture is not None:
            self.video_capture.release()
            self.video_capture = None
    
    def stop_preview(self):
        """停止预览"""
        try:
            self.stop_video_playback()
            self.preview_label.configure(image='', text="预览已停止")
            self.preview_label.image = None
            self._current_preview = None
            self.preview_filename_var.set("")
            self.stop_preview_btn.configure(state='disabled')
            
            if self._preview_timer:
                # 需要从外部传入root widget来取消定时器
                pass
                
            self._is_processing = False
            
        except Exception as e:
            print(f"停止预览时出错: {str(e)}")
    
    def clear_cache(self):
        """清理预览缓存"""
        self._preview_cache.clear()
    
    def cleanup_invalid_cache(self):
        """清理无效的预览缓存"""
        try:
            keys_to_remove = []
            for path in self._preview_cache.keys():
                if not os.path.exists(path):
                    keys_to_remove.append(path)
            
            for path in keys_to_remove:
                del self._preview_cache[path]
                
        except Exception as e:
            print(f"清理预览缓存失败: {e}")
    
    def __del__(self):
        """清理资源"""
        self.stop_video_playback()
        self.clear_cache()
