"""
测试目录树按钮删除功能
验证刷新、展开、折叠按钮是否已成功删除
"""
import tkinter as tk
import time


def test_directory_tree_buttons_removal():
    """测试目录树按钮删除"""
    print("=" * 60)
    print("目录树按钮删除测试")
    print("=" * 60)
    
    try:
        # 导入主程序
        import sys
        sys.path.append('.')
        
        # 创建主窗口
        root = tk.Tk()
        root.title("目录树按钮删除测试")
        root.geometry("1200x800")
        
        # 导入并创建应用实例
        from 重复文件查找器 import FileSearchApp
        app = FileSearchApp(root)
        
        print("1. 检查目录树按钮是否已删除...")
        
        # 检查应该被删除的按钮属性
        removed_buttons = [
            ('refresh_tree_btn', '刷新按钮'),
            ('expand_all_btn', '展开所有按钮'),
            ('collapse_all_btn', '折叠所有按钮'),
            ('dir_control_frame', '目录控制框架')
        ]
        
        all_buttons_removed = True
        for attr_name, display_name in removed_buttons:
            if hasattr(app, attr_name):
                print(f"❌ {display_name}仍然存在")
                all_buttons_removed = False
            else:
                print(f"✓ {display_name}已删除")
        
        if all_buttons_removed:
            print("✓ 所有目录树控制按钮已成功删除")
        else:
            print("❌ 部分按钮未删除")
            return False
        
        print("2. 检查目录树组件是否正常...")
        
        # 检查目录树本身是否仍然存在和正常工作
        essential_components = [
            ('directory_tree', '目录树组件'),
            ('directory_frame', '目录框架'),
            ('dir_tree_container', '目录树容器')
        ]
        
        all_components_exist = True
        for attr_name, display_name in essential_components:
            if hasattr(app, attr_name):
                print(f"✓ {display_name}存在")
            else:
                print(f"❌ {display_name}不存在")
                all_components_exist = False
        
        if not all_components_exist:
            print("❌ 目录树核心组件缺失")
            return False
        
        print("3. 检查删除的方法是否已移除...")
        
        # 检查应该被删除的方法
        removed_methods = [
            ('refresh_directory_tree', '刷新整个目录树方法'),
            ('refresh_directory_item', '刷新目录项方法'),
            ('expand_all_directories', '展开所有目录方法'),
            ('collapse_all_directories', '折叠所有目录方法')
        ]
        
        all_methods_removed = True
        for method_name, display_name in removed_methods:
            if hasattr(app, method_name):
                print(f"❌ {display_name}仍然存在")
                all_methods_removed = False
            else:
                print(f"✓ {display_name}已删除")
        
        if all_methods_removed:
            print("✓ 所有相关方法已成功删除")
        else:
            print("❌ 部分方法未删除")
            return False
        
        print("4. 检查目录树基本功能是否正常...")
        
        # 检查目录树的基本功能是否仍然正常
        if hasattr(app, 'directory_tree') and app.directory_tree:
            # 检查是否有根节点
            root_items = app.directory_tree.get_children()
            if root_items:
                print(f"✓ 目录树有 {len(root_items)} 个根节点")
            else:
                print("❌ 目录树没有根节点")
                return False
            
            # 检查事件绑定是否正常
            essential_events = [
                ('<<TreeviewSelect>>', '选择事件'),
                ('<Double-1>', '双击事件'),
                ('<Button-3>', '右键事件'),
                ('<<TreeviewOpen>>', '展开事件'),
                ('<<TreeviewClose>>', '折叠事件')
            ]
            
            # 这里我们无法直接检查事件绑定，但可以检查相关方法是否存在
            essential_methods = [
                ('on_directory_select', '目录选择事件处理'),
                ('on_directory_double_click', '目录双击事件处理'),
                ('on_directory_right_click', '目录右键事件处理'),
                ('on_directory_expand', '目录展开事件处理'),
                ('on_directory_collapse', '目录折叠事件处理')
            ]
            
            all_event_methods_exist = True
            for method_name, display_name in essential_methods:
                if hasattr(app, method_name):
                    print(f"✓ {display_name}方法存在")
                else:
                    print(f"❌ {display_name}方法不存在")
                    all_event_methods_exist = False
            
            if not all_event_methods_exist:
                print("❌ 部分事件处理方法缺失")
                return False
        else:
            print("❌ 目录树组件不存在或未初始化")
            return False
        
        print("5. 检查界面布局是否正常...")
        
        # 检查界面布局是否因为删除按钮而受到影响
        try:
            root.update()
            
            # 检查目录框架是否可见
            if app.directory_frame.winfo_viewable():
                print("✓ 目录框架可见")
            else:
                print("❌ 目录框架不可见")
                return False
            
            # 检查目录树是否可见
            if app.directory_tree.winfo_viewable():
                print("✓ 目录树可见")
            else:
                print("❌ 目录树不可见")
                return False
            
        except Exception as e:
            print(f"❌ 界面布局检查失败: {e}")
            return False
        
        print("6. 测试右键菜单是否正常...")
        
        # 检查右键菜单相关方法是否存在
        if hasattr(app, 'on_directory_right_click'):
            print("✓ 右键菜单功能存在")
            
            # 检查相关的辅助方法
            menu_methods = [
                ('set_current_directory', '设为当前目录方法'),
                ('open_in_explorer', '在文件管理器中打开方法')
            ]
            
            all_menu_methods_exist = True
            for method_name, display_name in menu_methods:
                if hasattr(app, method_name):
                    print(f"✓ {display_name}存在")
                else:
                    print(f"❌ {display_name}不存在")
                    all_menu_methods_exist = False
            
            if not all_menu_methods_exist:
                print("❌ 部分右键菜单方法缺失")
                return False
        else:
            print("❌ 右键菜单功能不存在")
            return False
        
        print("7. 所有测试通过！")
        
        # 关闭窗口
        root.after(2000, root.quit)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    try:
        success = test_directory_tree_buttons_removal()
        
        if success:
            print("\n🎉 目录树按钮删除验证成功！")
            print("✓ 所有控制按钮已成功删除")
            print("✓ 相关方法已清理")
            print("✓ 目录树核心功能正常")
            print("✓ 界面布局保持正常")
            print("✓ 右键菜单功能完整")
            print("\n简化后的目录树特点:")
            print("• 界面更简洁，减少用户困惑")
            print("• 避免了容易出错的批量操作")
            print("• 保留了核心的浏览和选择功能")
            print("• 右键菜单提供必要的操作选项")
            print("• 双击展开/折叠功能仍然可用")
        else:
            print("\n❌ 验证失败！需要进一步检查。")
            
    except Exception as e:
        print(f"验证过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
