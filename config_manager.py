"""
配置管理器模块 - 负责配置文件的读写和管理
"""
import os
import json
import datetime
from pathlib import Path


class ConfigManager:
    """配置管理器 - 专门负责配置文件管理"""
    
    def __init__(self):
        self.config_file = os.path.join(os.path.expanduser("~"), ".same_size_files_config.json")
        self.delete_log_file = r"z:\work\same_size_files_delete_index.txt"
        
        # 默认配置
        self.default_config = {
            "last_directory": "",
            "last_loaded_file": "",
            "window_geometry": "1400x768",
            "include_images": False,
            "same_dir_selection": False,
            "same_time_selection": False,
            "preview_cache_size": 10,
            "file_cache_size": 1000,
            "auto_save_results": True,
            "scan_thread_count": 4
        }
        
        self.config = self.default_config.copy()
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # 合并配置，保留默认值
                    self.config.update(loaded_config)
                    print(f"配置已加载: {self.config_file}")
            else:
                print("配置文件不存在，使用默认配置")
        except Exception as e:
            print(f"加载配置失败，使用默认配置: {str(e)}")
            self.config = self.default_config.copy()
    
    def save_config(self):
        """保存配置文件"""
        try:
            # 确保配置目录存在
            config_dir = os.path.dirname(self.config_file)
            os.makedirs(config_dir, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            print(f"配置已保存: {self.config_file}")
        except Exception as e:
            print(f"保存配置失败: {str(e)}")
    
    def get(self, key, default=None):
        """获取配置值"""
        return self.config.get(key, default)
    
    def set(self, key, value):
        """设置配置值"""
        self.config[key] = value
    
    def update(self, **kwargs):
        """批量更新配置"""
        self.config.update(kwargs)
    
    def save_results_to_file(self, results, directory, filename=None):
        """保存搜索结果到文件"""
        try:
            if filename is None:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"duplicate_files_{timestamp}.json"
            
            # 确保保存目录存在
            save_dir = os.path.join(os.path.expanduser("~"), "Documents", "DuplicateFiles")
            os.makedirs(save_dir, exist_ok=True)
            
            filepath = os.path.join(save_dir, filename)
            
            # 准备保存的数据
            save_data = {
                "directory": directory,
                "timestamp": datetime.datetime.now().isoformat(),
                "results": results,
                "total_groups": len(results),
                "total_files": sum(len(files) for files in results.values())
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            
            # 更新最后加载的文件
            self.set("last_loaded_file", filepath)
            self.save_config()
            
            return filepath
            
        except Exception as e:
            print(f"保存结果失败: {str(e)}")
            return None
    
    def load_results_from_file(self, filepath):
        """从文件加载搜索结果"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 更新最后加载的文件
            self.set("last_loaded_file", filepath)
            self.save_config()
            
            return data
            
        except Exception as e:
            print(f"加载结果失败: {str(e)}")
            return None
    
    def log_deleted_file(self, file_path, size, timestamp=None):
        """记录删除的文件"""
        try:
            if timestamp is None:
                timestamp = datetime.datetime.now().isoformat()
            
            # 确保日志目录存在
            log_dir = os.path.dirname(self.delete_log_file)
            os.makedirs(log_dir, exist_ok=True)
            
            log_entry = f"{timestamp}\t{size}\t{file_path}\n"
            
            with open(self.delete_log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
                
        except Exception as e:
            print(f"记录删除日志失败: {str(e)}")
    
    def get_recent_results_files(self, limit=10):
        """获取最近的结果文件列表"""
        try:
            save_dir = os.path.join(os.path.expanduser("~"), "Documents", "DuplicateFiles")
            if not os.path.exists(save_dir):
                return []
            
            files = []
            for filename in os.listdir(save_dir):
                if filename.endswith('.json') and filename.startswith('duplicate_files_'):
                    filepath = os.path.join(save_dir, filename)
                    mtime = os.path.getmtime(filepath)
                    files.append((filepath, mtime, filename))
            
            # 按修改时间排序，最新的在前
            files.sort(key=lambda x: x[1], reverse=True)
            
            return [f[0] for f in files[:limit]]
            
        except Exception as e:
            print(f"获取最近文件列表失败: {str(e)}")
            return []
    
    def cleanup_old_results(self, keep_days=30):
        """清理旧的结果文件"""
        try:
            save_dir = os.path.join(os.path.expanduser("~"), "Documents", "DuplicateFiles")
            if not os.path.exists(save_dir):
                return
            
            cutoff_time = datetime.datetime.now() - datetime.timedelta(days=keep_days)
            cutoff_timestamp = cutoff_time.timestamp()
            
            cleaned_count = 0
            for filename in os.listdir(save_dir):
                if filename.endswith('.json') and filename.startswith('duplicate_files_'):
                    filepath = os.path.join(save_dir, filename)
                    if os.path.getmtime(filepath) < cutoff_timestamp:
                        os.remove(filepath)
                        cleaned_count += 1
            
            if cleaned_count > 0:
                print(f"清理了 {cleaned_count} 个旧的结果文件")
                
        except Exception as e:
            print(f"清理旧文件失败: {str(e)}")
    
    def export_config(self, filepath):
        """导出配置到指定文件"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"导出配置失败: {str(e)}")
            return False
    
    def import_config(self, filepath):
        """从指定文件导入配置"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 验证配置的有效性
            if isinstance(imported_config, dict):
                # 只更新存在的配置项
                for key, value in imported_config.items():
                    if key in self.default_config:
                        self.config[key] = value
                
                self.save_config()
                return True
            else:
                print("无效的配置文件格式")
                return False
                
        except Exception as e:
            print(f"导入配置失败: {str(e)}")
            return False
    
    def reset_to_default(self):
        """重置为默认配置"""
        self.config = self.default_config.copy()
        self.save_config()
        print("配置已重置为默认值")
    
    def validate_config(self):
        """验证配置的有效性"""
        try:
            # 检查必要的配置项
            for key in self.default_config:
                if key not in self.config:
                    self.config[key] = self.default_config[key]
                    print(f"添加缺失的配置项: {key}")
            
            # 检查数值类型的配置
            numeric_configs = {
                'preview_cache_size': (1, 100),
                'file_cache_size': (100, 10000),
                'scan_thread_count': (1, 16)
            }
            
            for key, (min_val, max_val) in numeric_configs.items():
                if key in self.config:
                    value = self.config[key]
                    if not isinstance(value, int) or value < min_val or value > max_val:
                        self.config[key] = self.default_config[key]
                        print(f"修正无效的配置项: {key}")
            
            return True
            
        except Exception as e:
            print(f"验证配置失败: {str(e)}")
            return False
