"""
测试相同大小选择功能
"""
import os
import tempfile
import shutil
import tkinter as tk
import time


def create_test_files_with_different_sizes():
    """创建不同大小的测试文件"""
    base_dir = tempfile.mkdtemp(prefix="size_test_")
    print(f"创建测试基础目录: {base_dir}")
    
    # 创建多个组的文件，每组内文件大小不同
    test_groups = {
        "组1": {
            "dir1": [
                ("small1.txt", "小文件内容"),
                ("medium1.txt", "中等文件内容" * 10),
                ("large1.txt", "大文件内容" * 100)
            ],
            "dir2": [
                ("small2.txt", "小文件内容2"),
                ("medium2.txt", "中等文件内容2" * 10),
                ("large2.txt", "大文件内容2" * 100)
            ]
        },
        "组2": {
            "dir3": [
                ("tiny1.txt", "微小"),
                ("normal1.txt", "正常大小文件" * 20),
                ("huge1.txt", "巨大文件" * 200)
            ],
            "dir4": [
                ("tiny2.txt", "微小2"),
                ("normal2.txt", "正常大小文件2" * 20),
                ("huge2.txt", "巨大文件2" * 200)
            ]
        }
    }
    
    created_files = {}
    
    for group_name, dirs in test_groups.items():
        created_files[group_name] = {}
        for dir_name, files in dirs.items():
            dir_path = os.path.join(base_dir, dir_name)
            os.makedirs(dir_path, exist_ok=True)
            created_files[group_name][dir_name] = []
            
            for filename, content in files:
                file_path = os.path.join(dir_path, filename)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                created_files[group_name][dir_name].append(file_path)
                print(f"创建文件: {file_path} (大小: {len(content)} 字节)")
    
    return base_dir, created_files


def test_same_size_selection():
    """测试相同大小选择功能"""
    print("开始测试相同大小选择功能...")
    
    # 创建测试文件
    test_base_dir, created_files = create_test_files_with_different_sizes()
    
    try:
        # 导入主程序
        import sys
        sys.path.append('.')
        
        # 创建主窗口
        root = tk.Tk()
        root.title("相同大小选择功能测试")
        root.geometry("1400x800")
        
        # 导入并创建应用实例
        from 重复文件查找器 import FileSearchApp
        app = FileSearchApp(root)
        
        print("1. 检查相同大小选择控件是否存在...")
        
        # 检查新增的控件
        controls_to_check = [
            ('same_size_var', '相同大小变量'),
            ('same_size_checkbox', '相同大小勾选框'),
            ('size_preference_var', '大小偏好变量'),
            ('size_larger_radio', '偏大单选按钮'),
            ('size_smaller_radio', '偏小单选按钮')
        ]
        
        all_controls_exist = True
        for attr_name, display_name in controls_to_check:
            if hasattr(app, attr_name):
                print(f"✓ {display_name}存在")
            else:
                print(f"❌ {display_name}不存在")
                all_controls_exist = False
        
        if not all_controls_exist:
            print("❌ 部分控件缺失，测试终止")
            return False
        
        print("2. 检查控件初始状态...")
        
        # 检查初始状态
        same_size_initial = app.same_size_var.get()
        size_preference_initial = app.size_preference_var.get()
        
        print(f"相同大小勾选框初始状态: {same_size_initial}")
        print(f"大小偏好初始值: {size_preference_initial}")
        
        if size_preference_initial == "larger":
            print("✓ 大小偏好默认为'偏大'")
        else:
            print(f"❌ 大小偏好默认值不正确: {size_preference_initial}")
        
        print("3. 设置测试目录...")
        
        # 设置当前目录
        app.current_directory = test_base_dir
        app.path_var.set(f"当前文件夹: {test_base_dir}")
        app.search_by_size_btn.configure(state='normal')
        app.search_by_duration_btn.configure(state='normal')
        
        print(f"设置测试目录: {test_base_dir}")
        
        print("4. 模拟搜索相同大小文件...")
        
        # 模拟搜索结果（创建假的搜索结果）
        mock_results = {}
        
        # 收集所有文件并按大小分组
        all_files = []
        for group_name, dirs in created_files.items():
            for dir_name, files in dirs.items():
                all_files.extend(files)
        
        # 按实际文件大小分组
        size_groups = {}
        for file_path in all_files:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                if file_size not in size_groups:
                    size_groups[file_size] = []
                size_groups[file_size].append(file_path)
        
        # 只保留有多个文件的组
        for size, files in size_groups.items():
            if len(files) > 1:
                mock_results[size] = files
        
        app.current_results = mock_results
        print(f"模拟搜索结果: {len(mock_results)} 个大小组")
        
        # 创建勾选框变量
        app.checkbox_vars = {}
        for size, files in mock_results.items():
            for file_path in files:
                app.checkbox_vars[file_path] = tk.BooleanVar()
        
        print("5. 测试相同大小选择功能...")
        
        def test_size_selection():
            try:
                # 启用相同大小选择
                app.same_size_var.set(True)
                print("✓ 启用相同大小选择")
                
                # 测试偏大选择
                app.size_preference_var.set("larger")
                print("设置偏好为'偏大'")
                
                # 选择一个文件来触发相同大小选择
                if mock_results:
                    first_group = list(mock_results.values())[0]
                    if len(first_group) > 1:
                        test_file = first_group[0]
                        test_var = app.checkbox_vars[test_file]
                        
                        print(f"选择测试文件: {os.path.basename(test_file)}")
                        
                        # 模拟勾选框点击
                        test_var.set(True)
                        app.on_checkbox_click(test_file, test_var)
                        root.update()
                        
                        # 检查是否正确选择了偏大的文件
                        selected_count = 0
                        for file_path, var in app.checkbox_vars.items():
                            if var.get():
                                selected_count += 1
                        
                        print(f"选择后共有 {selected_count} 个文件被选中")
                        
                        if selected_count > 1:
                            print("✓ 相同大小选择功能工作正常")
                        else:
                            print("❌ 相同大小选择功能可能有问题")
                
                # 测试偏小选择
                print("6. 测试偏小选择...")
                
                # 先清空所有选择
                for var in app.checkbox_vars.values():
                    var.set(False)
                
                app.size_preference_var.set("smaller")
                print("设置偏好为'偏小'")
                
                # 再次选择文件
                if mock_results:
                    first_group = list(mock_results.values())[0]
                    if len(first_group) > 1:
                        test_file = first_group[0]
                        test_var = app.checkbox_vars[test_file]
                        
                        test_var.set(True)
                        app.on_checkbox_click(test_file, test_var)
                        root.update()
                        
                        # 检查选择结果
                        selected_count = 0
                        for file_path, var in app.checkbox_vars.items():
                            if var.get():
                                selected_count += 1
                        
                        print(f"偏小选择后共有 {selected_count} 个文件被选中")
                
                print("7. 测试方法是否存在...")
                
                # 检查相关方法是否存在
                methods_to_check = [
                    '_handle_same_size_selection',
                    'on_checkbox_click'
                ]
                
                for method_name in methods_to_check:
                    if hasattr(app, method_name):
                        print(f"✓ 方法 {method_name} 存在")
                    else:
                        print(f"❌ 方法 {method_name} 不存在")
                
                print("8. 所有测试完成！")
                return True
                
            except Exception as e:
                print(f"❌ 测试过程中出错: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        # 在主线程中执行测试
        root.after(500, lambda: test_and_exit(test_size_selection, root))
        
        # 运行主循环
        root.mainloop()
        
    finally:
        # 清理测试目录
        try:
            shutil.rmtree(test_base_dir)
            print(f"清理测试目录: {test_base_dir}")
        except Exception as e:
            print(f"清理测试目录失败: {e}")


def test_and_exit(test_func, root):
    """执行测试并退出"""
    try:
        success = test_func()
        if success:
            print("\n🎉 相同大小选择功能测试通过！")
            print("✓ 界面控件创建成功")
            print("✓ 相关方法存在")
            print("✓ 基本功能正常")
        else:
            print("\n❌ 测试失败！需要进一步检查实现。")
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
    finally:
        # 延迟关闭，让用户看到结果
        root.after(3000, root.quit)


def main():
    """主函数"""
    print("=" * 50)
    print("相同大小选择功能测试")
    print("=" * 50)
    
    test_same_size_selection()


if __name__ == "__main__":
    main()
