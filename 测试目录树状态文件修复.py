"""
测试目录树状态文件修复
"""
import tkinter as tk
import sys
import os


def test_directory_tree_state_file_fix():
    """测试目录树状态文件修复"""
    print("开始测试目录树状态文件修复...")
    
    try:
        # 导入主程序
        sys.path.append('.')
        
        # 创建主窗口
        root = tk.Tk()
        root.title("目录树状态文件修复测试")
        root.geometry("800x600")
        
        print("1. 尝试创建FileSearchApp实例...")
        
        # 导入并创建应用实例
        from 重复文件查找器 import FileSearchApp
        app = FileSearchApp(root)
        
        print("✓ FileSearchApp实例创建成功")
        
        print("2. 检查directory_tree_state_file属性...")
        
        # 检查属性是否存在
        if hasattr(app, 'directory_tree_state_file'):
            print(f"✓ directory_tree_state_file属性存在: {app.directory_tree_state_file}")
        else:
            print("❌ directory_tree_state_file属性不存在")
            return False
        
        print("3. 检查expanded_directories属性...")
        
        # 检查expanded_directories属性
        if hasattr(app, 'expanded_directories'):
            print(f"✓ expanded_directories属性存在: {type(app.expanded_directories)}")
        else:
            print("❌ expanded_directories属性不存在")
            return False
        
        print("4. 检查目录树组件...")
        
        # 检查目录树是否创建成功
        if hasattr(app, 'directory_tree'):
            print("✓ 目录树组件创建成功")
        else:
            print("❌ 目录树组件未创建")
            return False
        
        print("5. 测试状态文件相关方法...")
        
        # 测试保存状态方法
        try:
            app.save_directory_tree_state()
            print("✓ save_directory_tree_state方法执行成功")
        except Exception as e:
            print(f"❌ save_directory_tree_state方法执行失败: {e}")
            return False
        
        # 测试加载状态方法
        try:
            app.load_directory_tree_state()
            print("✓ load_directory_tree_state方法执行成功")
        except Exception as e:
            print(f"❌ load_directory_tree_state方法执行失败: {e}")
            return False
        
        print("6. 测试状态文件路径...")
        
        # 检查状态文件路径是否合理
        state_file = app.directory_tree_state_file
        if state_file and os.path.expanduser("~") in state_file:
            print(f"✓ 状态文件路径合理: {state_file}")
        else:
            print(f"❌ 状态文件路径不合理: {state_file}")
            return False
        
        print("7. 所有测试通过！")
        
        # 关闭窗口
        root.after(2000, root.quit)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("=" * 50)
    print("目录树状态文件修复测试")
    print("=" * 50)
    
    success = test_directory_tree_state_file_fix()
    
    if success:
        print("\n🎉 修复成功！目录树状态文件功能正常。")
        print("✓ directory_tree_state_file属性正确定义")
        print("✓ 初始化顺序问题已解决")
        print("✓ 状态文件相关方法正常工作")
    else:
        print("\n❌ 修复失败！需要进一步检查。")


if __name__ == "__main__":
    main()
