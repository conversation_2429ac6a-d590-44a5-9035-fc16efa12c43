"""
文件扫描器模块 - 负责文件扫描和信息提取
"""
import os
import threading
import time
from collections import defaultdict
from pathlib import Path
import cv2
from PIL import Image
import mimetypes


class FileScanner:
    """文件扫描器类 - 专门负责文件扫描和信息提取"""
    
    def __init__(self):
        self.is_scanning = False
        self.scan_cancelled = False
        self._file_cache = {}  # 文件信息缓存
        
    def scan_files_by_size(self, directory, include_images=False, progress_callback=None, status_callback=None):
        """按文件大小扫描重复文件"""
        self.is_scanning = True
        self.scan_cancelled = False
        
        try:
            size_dict = defaultdict(list)
            total_files = self._count_files(directory)
            processed_files = 0
            
            for root, dirs, files in os.walk(directory):
                if self.scan_cancelled:
                    break
                    
                for file in files:
                    if self.scan_cancelled:
                        break
                        
                    file_path = os.path.join(root, file)
                    
                    # 更新状态
                    if status_callback:
                        status_callback(f"正在处理: {file}")
                    
                    try:
                        # 检查是否需要包含图片文件
                        if not include_images and self._is_image_file(file_path):
                            continue
                            
                        # 获取文件大小（使用缓存）
                        file_size = self._get_file_size_cached(file_path)
                        if file_size > 0:
                            size_dict[file_size].append(file_path)
                            
                    except (OSError, IOError):
                        continue
                    
                    processed_files += 1
                    if progress_callback and total_files > 0:
                        progress = (processed_files / total_files) * 100
                        progress_callback(progress)
            
            # 过滤出重复文件（大小相同且文件数量>1）
            duplicate_files = {size: files for size, files in size_dict.items() if len(files) > 1}
            
            return duplicate_files
            
        finally:
            self.is_scanning = False
    
    def scan_files_by_duration(self, directory, progress_callback=None, status_callback=None):
        """按视频时长扫描重复文件"""
        self.is_scanning = True
        self.scan_cancelled = False
        
        try:
            duration_dict = defaultdict(list)
            total_files = self._count_video_files(directory)
            processed_files = 0
            
            for root, dirs, files in os.walk(directory):
                if self.scan_cancelled:
                    break
                    
                for file in files:
                    if self.scan_cancelled:
                        break
                        
                    file_path = os.path.join(root, file)
                    
                    # 只处理视频文件
                    if not self._is_video_file(file_path):
                        continue
                    
                    # 更新状态
                    if status_callback:
                        status_callback(f"正在处理: {file}")
                    
                    try:
                        # 获取视频时长（使用缓存）
                        duration = self._get_video_duration_cached(file_path)
                        if duration > 0:
                            duration_dict[duration].append(file_path)
                            
                    except Exception:
                        continue
                    
                    processed_files += 1
                    if progress_callback and total_files > 0:
                        progress = (processed_files / total_files) * 100
                        progress_callback(progress)
            
            # 过滤出重复文件
            duplicate_files = {duration: files for duration, files in duration_dict.items() if len(files) > 1}
            
            return duplicate_files
            
        finally:
            self.is_scanning = False
    
    def cancel_scan(self):
        """取消扫描"""
        self.scan_cancelled = True
    
    def _count_files(self, directory):
        """计算目录中的文件总数"""
        try:
            count = 0
            for root, dirs, files in os.walk(directory):
                count += len(files)
            return count
        except:
            return 0
    
    def _count_video_files(self, directory):
        """计算目录中的视频文件总数"""
        try:
            count = 0
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if self._is_video_file(os.path.join(root, file)):
                        count += 1
            return count
        except:
            return 0
    
    def _is_image_file(self, file_path):
        """判断是否为图片文件"""
        try:
            mime_type, _ = mimetypes.guess_type(file_path)
            return mime_type and mime_type.startswith('image/')
        except:
            return False
    
    def _is_video_file(self, file_path):
        """判断是否为视频文件"""
        try:
            mime_type, _ = mimetypes.guess_type(file_path)
            return mime_type and mime_type.startswith('video/')
        except:
            return False
    
    def _get_file_size_cached(self, file_path):
        """获取文件大小（带缓存）"""
        try:
            # 检查缓存
            stat = os.stat(file_path)
            cache_key = f"{file_path}_{stat.st_mtime}"
            
            if cache_key in self._file_cache:
                return self._file_cache[cache_key]['size']
            
            # 获取文件大小
            file_size = stat.st_size
            
            # 缓存结果
            self._file_cache[cache_key] = {
                'size': file_size,
                'mtime': stat.st_mtime
            }
            
            return file_size
            
        except (OSError, IOError):
            return 0
    
    def _get_video_duration_cached(self, file_path):
        """获取视频时长（带缓存）"""
        try:
            # 检查缓存
            stat = os.stat(file_path)
            cache_key = f"{file_path}_{stat.st_mtime}_duration"
            
            if cache_key in self._file_cache:
                return self._file_cache[cache_key]['duration']
            
            # 获取视频时长
            cap = cv2.VideoCapture(file_path)
            if not cap.isOpened():
                return 0
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
            cap.release()
            
            if fps > 0:
                duration = int(frame_count / fps)
            else:
                duration = 0
            
            # 缓存结果
            self._file_cache[cache_key] = {
                'duration': duration,
                'mtime': stat.st_mtime
            }
            
            return duration
            
        except Exception:
            return 0
    
    def clear_cache(self):
        """清理缓存"""
        self._file_cache.clear()
    
    def cleanup_invalid_cache(self):
        """清理无效的缓存项"""
        try:
            keys_to_remove = []
            for key in self._file_cache.keys():
                file_path = key.split('_')[0]  # 简单提取文件路径
                if not os.path.exists(file_path):
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                del self._file_cache[key]
                
        except Exception as e:
            print(f"清理缓存失败: {e}")
