"""
测试切换目录、删除、移除文件时已选择文件区域的刷新
"""
import os
import tempfile
import shutil
import tkinter as tk
from tkinter import messagebox
import time


def create_test_files():
    """创建测试文件"""
    test_dir = tempfile.mkdtemp(prefix="selected_files_test_")
    print(f"创建测试目录: {test_dir}")
    
    # 创建不同大小的文件组
    file_groups = [
        (1024, 4),      # 1KB文件，4个
        (2048, 3),      # 2KB文件，3个  
        (4096, 5),      # 4KB文件，5个
    ]
    
    created_files = []
    
    for size, count in file_groups:
        for i in range(count):
            filename = f"test_{size}_{i:02d}.dat"
            filepath = os.path.join(test_dir, filename)
            
            with open(filepath, 'wb') as f:
                f.write(b'0' * size)
            
            created_files.append(filepath)
    
    print(f"创建了 {len(created_files)} 个测试文件")
    return test_dir, created_files


def create_second_test_dir():
    """创建第二个测试目录用于切换测试"""
    test_dir2 = tempfile.mkdtemp(prefix="selected_files_test2_")
    print(f"创建第二个测试目录: {test_dir2}")
    
    # 创建不同的文件
    file_groups = [
        (8192, 2),      # 8KB文件，2个
        (16384, 3),     # 16KB文件，3个
    ]
    
    created_files = []
    
    for size, count in file_groups:
        for i in range(count):
            filename = f"test2_{size}_{i:02d}.dat"
            filepath = os.path.join(test_dir2, filename)
            
            with open(filepath, 'wb') as f:
                f.write(b'0' * size)
            
            created_files.append(filepath)
    
    print(f"在第二个目录创建了 {len(created_files)} 个测试文件")
    return test_dir2, created_files


def test_selected_files_refresh():
    """测试已选择文件区域的刷新"""
    print("开始测试已选择文件区域刷新...")
    
    # 创建测试文件
    test_dir1, test_files1 = create_test_files()
    test_dir2, test_files2 = create_second_test_dir()
    
    try:
        # 导入主程序
        import sys
        sys.path.append('.')
        
        # 创建主窗口
        root = tk.Tk()
        root.title("已选择文件刷新测试")
        
        # 导入并创建应用实例
        from 重复文件查找器 import FileSearchApp
        app = FileSearchApp(root)
        
        print("1. 设置第一个测试目录并搜索...")
        
        # 设置第一个测试目录
        app.current_directory = test_dir1
        app.path_var.set(f"当前目录: {test_dir1}")
        app.search_by_size_btn.configure(state='normal')
        app.search_by_duration_btn.configure(state='normal')
        
        # 模拟测试过程
        def simulate_test():
            try:
                # 执行搜索
                app._search_by_size_worker()
                
                print("2. 检查搜索结果...")
                
                # 检查是否有结果
                if not app.current_results:
                    print("❌ 搜索没有找到重复文件")
                    return False
                
                print(f"✓ 找到 {len(app.current_results)} 组重复文件")
                
                # 检查已选择文件区域初始状态
                selected_frame_text = app.selected_files_frame.cget("text")
                if "0 个文件，0 字节" not in selected_frame_text:
                    print(f"❌ 已选择文件区域初始状态错误: {selected_frame_text}")
                    return False
                else:
                    print("✓ 已选择文件区域初始状态正确")
                
                print("3. 选择一些文件...")
                
                # 选择前几个文件
                selected_count = 0
                for file_path, var in list(app.checkbox_vars.items())[:3]:
                    var.set(1)
                    selected_count += 1
                
                # 手动触发更新（模拟用户点击）
                app.update_selected_files_tree()
                root.update()
                
                # 检查已选择文件区域是否更新
                selected_frame_text = app.selected_files_frame.cget("text")
                if f"{selected_count} 个文件" not in selected_frame_text:
                    print(f"❌ 选择文件后已选择文件区域未更新: {selected_frame_text}")
                    return False
                else:
                    print(f"✓ 选择文件后已选择文件区域正确更新: {selected_frame_text}")
                
                print("4. 测试切换目录...")
                
                # 切换到第二个目录
                app.current_directory = test_dir2
                app.path_var.set(f"当前文件夹: {test_dir2}")
                
                # 模拟切换目录的完整流程
                app.current_results = {}
                app.checkbox_vars = {}
                app.delete_buttons = {}
                app.hide_sort_buttons()
                app.update_selected_files_tree()
                
                root.update()
                
                # 检查已选择文件区域是否清空
                selected_frame_text = app.selected_files_frame.cget("text")
                if "0 个文件，0 字节" not in selected_frame_text:
                    print(f"❌ 切换目录后已选择文件区域未清空: {selected_frame_text}")
                    return False
                else:
                    print("✓ 切换目录后已选择文件区域正确清空")
                
                print("5. 在新目录搜索并测试删除...")
                
                # 在新目录搜索
                app._search_by_size_worker()
                
                if not app.current_results:
                    print("❌ 新目录搜索没有找到重复文件")
                    return False
                
                print(f"✓ 新目录找到 {len(app.current_results)} 组重复文件")
                
                # 选择一些文件
                selected_files = []
                for file_path, var in list(app.checkbox_vars.items())[:2]:
                    var.set(1)
                    selected_files.append(file_path)
                
                app.update_selected_files_tree()
                root.update()
                
                # 检查选择状态
                selected_frame_text = app.selected_files_frame.cget("text")
                if "2 个文件" not in selected_frame_text:
                    print(f"❌ 新目录选择文件后状态错误: {selected_frame_text}")
                    return False
                else:
                    print(f"✓ 新目录选择文件后状态正确: {selected_frame_text}")
                
                print("6. 测试删除文件...")
                
                # 删除选中的文件
                deleted_count = 0
                for file_path in selected_files:
                    try:
                        if os.path.exists(file_path):
                            os.remove(file_path)
                            deleted_count += 1
                            
                            # 从结果中移除
                            for size, files in list(app.current_results.items()):
                                if file_path in files:
                                    files.remove(file_path)
                                    if len(files) < 2:
                                        del app.current_results[size]
                                    break
                    except Exception as e:
                        print(f"删除文件失败: {e}")
                
                if deleted_count > 0:
                    # 模拟删除后的刷新
                    app.display_results(app.current_results, preserve_scroll=True)
                    app.update_selected_files_tree()
                    root.update()
                    
                    # 检查已选择文件区域是否更新
                    selected_frame_text = app.selected_files_frame.cget("text")
                    remaining_selected = len(selected_files) - deleted_count
                    
                    if remaining_selected == 0:
                        if "0 个文件，0 字节" not in selected_frame_text:
                            print(f"❌ 删除文件后已选择文件区域未正确更新: {selected_frame_text}")
                            return False
                        else:
                            print("✓ 删除文件后已选择文件区域正确更新（清空）")
                    else:
                        if f"{remaining_selected} 个文件" not in selected_frame_text:
                            print(f"❌ 删除文件后已选择文件区域未正确更新: {selected_frame_text}")
                            return False
                        else:
                            print(f"✓ 删除文件后已选择文件区域正确更新: {selected_frame_text}")
                
                print("7. 测试移除文件...")
                
                # 如果还有文件，测试移除功能
                if app.current_results:
                    # 选择一个文件进行移除测试
                    test_file = None
                    for file_path, var in app.checkbox_vars.items():
                        if os.path.exists(file_path):
                            var.set(1)
                            test_file = file_path
                            break
                    
                    if test_file:
                        app.update_selected_files_tree()
                        root.update()
                        
                        # 从结果中移除（不删除实际文件）
                        for size, files in list(app.current_results.items()):
                            if test_file in files:
                                files.remove(test_file)
                                if len(files) < 2:
                                    del app.current_results[size]
                                break
                        
                        # 模拟移除后的刷新
                        app.display_results(app.current_results, preserve_scroll=True)
                        app.update_selected_files_tree()
                        root.update()
                        
                        # 检查已选择文件区域
                        selected_frame_text = app.selected_files_frame.cget("text")
                        if "0 个文件，0 字节" not in selected_frame_text:
                            print(f"❌ 移除文件后已选择文件区域未正确更新: {selected_frame_text}")
                            return False
                        else:
                            print("✓ 移除文件后已选择文件区域正确更新")
                
                print("8. 所有测试通过！")
                return True
                
            except Exception as e:
                print(f"❌ 测试过程中出错: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        # 在主线程中执行测试
        root.after(100, lambda: test_and_exit(simulate_test, root))
        
        # 运行主循环
        root.mainloop()
        
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir1)
            print(f"清理第一个测试目录: {test_dir1}")
        except Exception as e:
            print(f"清理第一个测试目录失败: {e}")
        
        try:
            shutil.rmtree(test_dir2)
            print(f"清理第二个测试目录: {test_dir2}")
        except Exception as e:
            print(f"清理第二个测试目录失败: {e}")


def test_and_exit(test_func, root):
    """执行测试并退出"""
    try:
        success = test_func()
        if success:
            print("\n🎉 所有测试通过！已选择文件区域刷新功能正常工作。")
            print("✓ 切换目录时正确清空已选择文件")
            print("✓ 删除文件后正确更新已选择文件")
            print("✓ 移除文件后正确更新已选择文件")
        else:
            print("\n❌ 测试失败！需要进一步检查修复。")
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
    finally:
        # 延迟关闭，让用户看到结果
        root.after(3000, root.quit)


def main():
    """主函数"""
    print("=" * 50)
    print("已选择文件区域刷新测试")
    print("=" * 50)
    
    test_selected_files_refresh()


if __name__ == "__main__":
    main()
