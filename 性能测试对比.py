"""
性能测试对比脚本 - 对比原版和优化版的性能差异
"""
import os
import time
import psutil
import threading
from collections import defaultdict
import tempfile
import shutil
import random
import string

# 导入优化版模块
from file_scanner import FileScanner
from memory_manager import MemoryManager
from async_task_manager import AsyncTaskManager


class PerformanceTestSuite:
    """性能测试套件"""
    
    def __init__(self):
        self.test_directory = None
        self.test_files = []
        self.results = {}
        
    def create_test_data(self, num_files=1000, file_size_range=(1024, 1024*1024)):
        """创建测试数据"""
        print(f"创建测试数据: {num_files} 个文件...")
        
        # 创建临时测试目录
        self.test_directory = tempfile.mkdtemp(prefix="duplicate_test_")
        
        # 创建不同大小的文件组
        file_groups = {
            'small': (1024, 10*1024),      # 1KB - 10KB
            'medium': (100*1024, 1024*1024), # 100KB - 1MB
            'large': (5*1024*1024, 10*1024*1024)  # 5MB - 10MB
        }
        
        files_per_group = num_files // len(file_groups)
        
        for group_name, (min_size, max_size) in file_groups.items():
            group_dir = os.path.join(self.test_directory, group_name)
            os.makedirs(group_dir, exist_ok=True)
            
            # 创建一些重复文件
            duplicate_sizes = []
            for i in range(files_per_group // 4):  # 25%的文件是重复的
                size = random.randint(min_size, max_size)
                duplicate_sizes.append(size)
            
            for i in range(files_per_group):
                filename = f"test_file_{i:04d}.dat"
                filepath = os.path.join(group_dir, filename)
                
                # 决定文件大小
                if i < len(duplicate_sizes) * 3:  # 创建重复文件
                    size = duplicate_sizes[i % len(duplicate_sizes)]
                else:
                    size = random.randint(min_size, max_size)
                
                # 创建文件
                with open(filepath, 'wb') as f:
                    data = os.urandom(min(size, 1024*1024))  # 最多1MB的随机数据
                    remaining = size
                    while remaining > 0:
                        write_size = min(remaining, len(data))
                        f.write(data[:write_size])
                        remaining -= write_size
                
                self.test_files.append(filepath)
        
        print(f"测试数据创建完成: {len(self.test_files)} 个文件")
        print(f"测试目录: {self.test_directory}")
        
    def cleanup_test_data(self):
        """清理测试数据"""
        if self.test_directory and os.path.exists(self.test_directory):
            shutil.rmtree(self.test_directory)
            print("测试数据已清理")
    
    def measure_memory_usage(self):
        """测量内存使用"""
        process = psutil.Process()
        return process.memory_info().rss
    
    def test_original_method(self):
        """测试原始方法（模拟原版逻辑）"""
        print("\n=== 测试原始方法 ===")
        
        start_time = time.time()
        start_memory = self.measure_memory_usage()
        
        # 模拟原版的文件扫描逻辑
        size_dict = defaultdict(list)
        
        for root, dirs, files in os.walk(self.test_directory):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    file_size = os.path.getsize(file_path)
                    size_dict[file_size].append(file_path)
                except OSError:
                    continue
        
        # 过滤重复文件
        duplicate_files = {size: files for size, files in size_dict.items() if len(files) > 1}
        
        end_time = time.time()
        end_memory = self.measure_memory_usage()
        
        results = {
            'method': 'original',
            'execution_time': end_time - start_time,
            'memory_used': end_memory - start_memory,
            'peak_memory': end_memory,
            'duplicate_groups': len(duplicate_files),
            'total_duplicates': sum(len(files) for files in duplicate_files.values())
        }
        
        self.results['original'] = results
        self._print_results(results)
        
        return duplicate_files
    
    def test_optimized_method(self):
        """测试优化方法"""
        print("\n=== 测试优化方法 ===")
        
        start_time = time.time()
        start_memory = self.measure_memory_usage()
        
        # 使用优化版的文件扫描器
        scanner = FileScanner()
        
        # 模拟进度回调
        progress_values = []
        def progress_callback(progress):
            progress_values.append(progress)
        
        # 模拟状态回调
        status_messages = []
        def status_callback(status):
            status_messages.append(status)
        
        # 执行扫描
        duplicate_files = scanner.scan_files_by_size(
            self.test_directory,
            include_images=False,
            progress_callback=progress_callback,
            status_callback=status_callback
        )
        
        end_time = time.time()
        end_memory = self.measure_memory_usage()
        
        results = {
            'method': 'optimized',
            'execution_time': end_time - start_time,
            'memory_used': end_memory - start_memory,
            'peak_memory': end_memory,
            'duplicate_groups': len(duplicate_files),
            'total_duplicates': sum(len(files) for files in duplicate_files.values()),
            'progress_updates': len(progress_values),
            'status_updates': len(status_messages)
        }
        
        self.results['optimized'] = results
        self._print_results(results)
        
        return duplicate_files
    
    def test_async_method(self):
        """测试异步方法"""
        print("\n=== 测试异步方法 ===")
        
        start_time = time.time()
        start_memory = self.measure_memory_usage()
        
        # 使用异步任务管理器
        task_manager = AsyncTaskManager(max_workers=4)
        scanner = FileScanner()
        
        # 结果容器
        result_container = {'result': None, 'completed': False}
        
        def on_complete(result):
            result_container['result'] = result
            result_container['completed'] = True
        
        def on_error(error):
            print(f"异步扫描出错: {error}")
            result_container['completed'] = True
        
        # 提交异步任务
        task_id = task_manager.submit_task(
            scanner.scan_files_by_size,
            self.test_directory,
            False,  # include_images
            callback=on_complete,
            error_callback=on_error,
            task_name="异步扫描测试"
        )
        
        # 等待完成
        while not result_container['completed']:
            time.sleep(0.1)
        
        task_manager.shutdown()
        
        end_time = time.time()
        end_memory = self.measure_memory_usage()
        
        duplicate_files = result_container['result'] or {}
        
        results = {
            'method': 'async',
            'execution_time': end_time - start_time,
            'memory_used': end_memory - start_memory,
            'peak_memory': end_memory,
            'duplicate_groups': len(duplicate_files),
            'total_duplicates': sum(len(files) for files in duplicate_files.values())
        }
        
        self.results['async'] = results
        self._print_results(results)
        
        return duplicate_files
    
    def test_memory_management(self):
        """测试内存管理"""
        print("\n=== 测试内存管理 ===")
        
        memory_manager = MemoryManager(max_memory_mb=100)  # 限制100MB
        
        # 模拟大量缓存操作
        start_time = time.time()
        start_memory = self.measure_memory_usage()
        
        # 缓存大量文件信息
        for i, file_path in enumerate(self.test_files[:500]):  # 只测试前500个文件
            if os.path.exists(file_path):
                file_info = {
                    'size': os.path.getsize(file_path),
                    'mtime': os.path.getmtime(file_path),
                    'index': i
                }
                memory_manager.cache_file_info(file_path, file_info)
        
        mid_memory = self.measure_memory_usage()
        
        # 触发内存清理
        memory_manager._trigger_memory_cleanup()
        
        end_time = time.time()
        end_memory = self.measure_memory_usage()
        
        memory_manager.stop_memory_monitoring()
        
        results = {
            'method': 'memory_management',
            'execution_time': end_time - start_time,
            'memory_before_cache': start_memory,
            'memory_after_cache': mid_memory,
            'memory_after_cleanup': end_memory,
            'cache_stats': memory_manager.get_cache_statistics()
        }
        
        self.results['memory_management'] = results
        self._print_memory_results(results)
    
    def _print_results(self, results):
        """打印测试结果"""
        print(f"方法: {results['method']}")
        print(f"执行时间: {results['execution_time']:.2f} 秒")
        print(f"内存使用: {results['memory_used'] / 1024 / 1024:.1f} MB")
        print(f"峰值内存: {results['peak_memory'] / 1024 / 1024:.1f} MB")
        print(f"重复文件组: {results['duplicate_groups']}")
        print(f"重复文件总数: {results['total_duplicates']}")
        
        if 'progress_updates' in results:
            print(f"进度更新次数: {results['progress_updates']}")
        if 'status_updates' in results:
            print(f"状态更新次数: {results['status_updates']}")
    
    def _print_memory_results(self, results):
        """打印内存测试结果"""
        print(f"方法: {results['method']}")
        print(f"执行时间: {results['execution_time']:.2f} 秒")
        print(f"缓存前内存: {results['memory_before_cache'] / 1024 / 1024:.1f} MB")
        print(f"缓存后内存: {results['memory_after_cache'] / 1024 / 1024:.1f} MB")
        print(f"清理后内存: {results['memory_after_cleanup'] / 1024 / 1024:.1f} MB")
        print(f"缓存统计: {results['cache_stats']}")
    
    def compare_results(self):
        """对比测试结果"""
        print("\n" + "="*50)
        print("性能对比结果")
        print("="*50)
        
        if 'original' in self.results and 'optimized' in self.results:
            original = self.results['original']
            optimized = self.results['optimized']
            
            time_improvement = (original['execution_time'] - optimized['execution_time']) / original['execution_time'] * 100
            memory_improvement = (original['memory_used'] - optimized['memory_used']) / original['memory_used'] * 100
            
            print(f"执行时间改进: {time_improvement:.1f}%")
            print(f"内存使用改进: {memory_improvement:.1f}%")
            
            if time_improvement > 0:
                print("✓ 优化版执行速度更快")
            else:
                print("✗ 优化版执行速度较慢")
            
            if memory_improvement > 0:
                print("✓ 优化版内存使用更少")
            else:
                print("✗ 优化版内存使用更多")
    
    def run_all_tests(self, num_files=1000):
        """运行所有测试"""
        try:
            print("开始性能测试...")
            
            # 创建测试数据
            self.create_test_data(num_files)
            
            # 运行各种测试
            self.test_original_method()
            self.test_optimized_method()
            self.test_async_method()
            self.test_memory_management()
            
            # 对比结果
            self.compare_results()
            
        finally:
            # 清理测试数据
            self.cleanup_test_data()


def main():
    """主函数"""
    test_suite = PerformanceTestSuite()
    
    # 运行测试（可以调整文件数量）
    test_suite.run_all_tests(num_files=500)  # 减少文件数量以加快测试


if __name__ == "__main__":
    main()
