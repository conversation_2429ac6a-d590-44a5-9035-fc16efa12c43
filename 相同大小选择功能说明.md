# 相同大小选择功能实现说明

## 功能概述

在重复文件查找器中新增了"相同大小"选择功能，让用户可以在多组重复文件中快速选择偏大或偏小的文件，提高批量处理重复文件的效率。

## 用户需求

用户在处理重复文件时，经常需要：
- **保留较大的文件**：通常较大的文件质量更好或包含更多内容
- **删除较小的文件**：较小的文件可能是压缩版本或质量较低的副本
- **批量操作**：在多个重复文件组中应用相同的选择策略

## 界面设计

### 新增控件

在第二行按钮区域添加了以下控件：

```
[同目录选择] [相同时段] [相同大小] (偏大) (偏小)
```

#### 1. 相同大小勾选框
- **控件类型**：`ttk.Checkbutton`
- **文本**：`"相同大小"`
- **变量**：`self.same_size_var` (BooleanVar)
- **功能**：启用/禁用相同大小选择模式

#### 2. 大小偏好单选按钮
- **偏大选项**：
  - 控件类型：`ttk.Radiobutton`
  - 文本：`"偏大"`
  - 变量：`self.size_preference_var`
  - 值：`"larger"`

- **偏小选项**：
  - 控件类型：`ttk.Radiobutton`
  - 文本：`"偏小"`
  - 变量：`self.size_preference_var`
  - 值：`"smaller"`

### 界面代码实现

```python
# 相同大小选择选项
self.same_size_var = tk.BooleanVar()
self.same_size_checkbox = ttk.Checkbutton(self.button_row2, text="相同大小",
                                         variable=self.same_size_var)
self.same_size_checkbox.pack(side=tk.LEFT, padx=5)

# 大小选择方向（偏大或偏小）
self.size_preference_var = tk.StringVar(value="larger")
self.size_larger_radio = ttk.Radiobutton(self.button_row2, text="偏大", 
                                       variable=self.size_preference_var, value="larger")
self.size_larger_radio.pack(side=tk.LEFT, padx=2)

self.size_smaller_radio = ttk.Radiobutton(self.button_row2, text="偏小", 
                                        variable=self.size_preference_var, value="smaller")
self.size_smaller_radio.pack(side=tk.LEFT, padx=2)
```

## 功能逻辑

### 1. 选择触发机制

修改了 `on_checkbox_click` 方法，增加对相同大小选择的检测：

```python
def on_checkbox_click(self, file_path, var):
    """当勾选框状态改变时的处理"""
    # 检查启用的选择选项
    same_dir_enabled = self.same_dir_var.get()
    same_time_enabled = self.same_time_var.get()
    same_size_enabled = self.same_size_var.get()
    
    # 统计启用的选项数量
    enabled_options = sum([same_dir_enabled, same_time_enabled, same_size_enabled])
    
    if enabled_options > 1:
        # 多个选项同时启用，使用组合选择
        self._handle_combined_selection(file_path, var)
    elif same_size_enabled:
        # 只启用相同大小选择
        self._handle_same_size_selection(file_path, var)
    # ... 其他选项处理
```

### 2. 相同大小选择算法

实现了 `_handle_same_size_selection` 方法：

```python
def _handle_same_size_selection(self, file_path, var):
    """处理相同大小选择"""
    try:
        # 获取大小偏好设置
        prefer_larger = self.size_preference_var.get() == "larger"
        
        total_selected = 0
        selected_groups = 0
        
        # 遍历所有组
        for group_size, group_files in self.current_results.items():
            if len(group_files) > 1:
                # 获取该组中所有文件的实际大小
                file_sizes = []
                for f in group_files:
                    try:
                        if os.path.exists(f):
                            actual_size = os.path.getsize(f)
                            file_sizes.append((f, actual_size))
                    except (OSError, IOError):
                        continue
                
                if len(file_sizes) > 1:
                    # 按实际大小排序
                    file_sizes.sort(key=lambda x: x[1])
                    
                    # 根据偏好选择文件
                    if prefer_larger:
                        # 选择较大的文件（后半部分）
                        mid_point = len(file_sizes) // 2
                        target_files = [f for f, size in file_sizes[mid_point:]]
                    else:
                        # 选择较小的文件（前半部分）
                        mid_point = (len(file_sizes) + 1) // 2
                        target_files = [f for f, size in file_sizes[:mid_point]]
                    
                    # 如果当前文件在目标文件列表中，则选择所有目标文件
                    if file_path in target_files:
                        group_selected = 0
                        for target_file in target_files:
                            if target_file in self.checkbox_vars:
                                if var.get():  # 如果当前文件被选中
                                    self.checkbox_vars[target_file].set(1)
                                    group_selected += 1
                                else:  # 如果当前文件被取消选中
                                    self.checkbox_vars[target_file].set(0)
                        
                        if group_selected > 0:
                            total_selected += group_selected
                            selected_groups += 1
        
        # 显示状态信息
        if var.get() and total_selected > 0:
            size_type = "偏大" if prefer_larger else "偏小"
            self.show_status(f"已在 {selected_groups} 个组中选择{size_type}的文件，共 {total_selected} 个文件")
            
    except Exception as e:
        print(f"相同大小选择时出错: {str(e)}")
```

### 3. 大小比较逻辑

#### 文件大小获取
- 使用 `os.path.getsize(file_path)` 获取文件的实际字节大小
- 处理文件不存在或无权限访问的异常情况

#### 大小排序和分组
- 将同组文件按实际大小从小到大排序
- 计算中点位置，将文件分为"较小"和"较大"两部分

#### 选择策略
- **偏大选择**：选择后半部分文件（大于等于中位数的文件）
- **偏小选择**：选择前半部分文件（小于等于中位数的文件）

### 4. 组合选择支持

当用户同时启用多个选择选项时，系统会调用组合选择逻辑：

- **同目录 + 相同大小**：在同目录文件中选择偏大或偏小的文件
- **相同时段 + 相同大小**：在相同时段文件中选择偏大或偏小的文件
- **三选项组合**：同时满足目录、时段和大小条件

## 使用场景

### 1. 图片文件处理
```
场景：同一张图片有多个不同分辨率的版本
操作：启用"相同大小"，选择"偏大"
结果：自动选择高分辨率版本，删除低分辨率副本
```

### 2. 文档文件整理
```
场景：同一文档有完整版和摘要版
操作：启用"相同大小"，选择"偏大"
结果：保留完整版文档，删除摘要版
```

### 3. 音视频文件管理
```
场景：同一内容有不同质量的编码版本
操作：启用"相同大小"，选择"偏大"
结果：保留高质量版本，删除压缩版本
```

### 4. 组合使用场景
```
场景：在特定目录中选择较新且较大的文件
操作：同时启用"同目录选择"和"相同大小"（偏大）
结果：在每个目录中选择较大的文件版本
```

## 用户操作流程

### 基本操作流程
```
1. 搜索重复文件（相同大小或相同时长）
   ↓
2. 启用"相同大小"勾选框
   ↓
3. 选择偏好：偏大 或 偏小
   ↓
4. 点击任意一个文件的勾选框
   ↓
5. 系统自动在所有组中选择相应大小的文件
   ↓
6. 确认选择结果，执行删除或其他操作
```

### 组合操作流程
```
1. 搜索重复文件
   ↓
2. 同时启用多个选择选项（如"同目录选择" + "相同大小"）
   ↓
3. 设置相应的偏好（目录、时段、大小）
   ↓
4. 点击符合条件的文件勾选框
   ↓
5. 系统应用组合条件进行智能选择
```

## 技术特点

### 1. 精确的大小比较
- 使用文件系统的实际字节大小，而不是显示大小
- 处理大文件时的性能优化
- 支持各种文件类型的大小比较

### 2. 智能的分组算法
- 动态计算中点位置
- 处理奇数和偶数个文件的情况
- 确保选择结果的合理性

### 3. 灵活的组合选择
- 支持多种选择条件的组合
- 优先级处理和冲突解决
- 用户友好的状态反馈

### 4. 健壮的错误处理
- 文件访问权限检查
- 文件不存在的处理
- 异常情况的优雅降级

## 状态反馈

系统会在状态栏显示详细的选择结果：

```
"已在 3 个组中选择偏大的文件，共 8 个文件"
"已在 2 个组中选择目录 'Documents' 的偏小文件，共 5 个文件"
```

## 性能考虑

### 1. 文件大小缓存
- 避免重复获取同一文件的大小信息
- 批量处理减少系统调用

### 2. 算法优化
- 使用高效的排序算法
- 最小化文件系统访问次数

### 3. 内存管理
- 及时释放不需要的文件信息
- 避免大量文件时的内存溢出

## 总结

相同大小选择功能的添加显著提升了重复文件处理的效率：

1. **操作简化**：一键选择所有组中的偏大或偏小文件
2. **智能化**：基于文件实际大小的精确比较
3. **灵活性**：支持与其他选择条件的组合使用
4. **用户友好**：清晰的界面设计和状态反馈

这个功能特别适合处理大量重复文件的场景，帮助用户快速做出基于文件大小的批量选择决策。
