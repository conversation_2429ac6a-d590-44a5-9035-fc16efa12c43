# 目录树记忆功能使用说明

## 功能简介

重复文件查找器现在具备了目录树记忆功能！这意味着程序会自动记住你展开的目录状态，下次启动时会自动恢复这些展开状态，让你无需重复展开常用的目录。

## 主要特性

### 🧠 智能记忆
- **自动保存**：程序退出时自动保存目录展开状态
- **自动恢复**：程序启动时自动恢复之前的展开状态
- **实时更新**：每次展开或折叠目录时都会更新记忆

### ⏰ 时效管理
- **7天有效期**：记忆状态在7天内有效，过期自动清理
- **智能清理**：自动移除不存在的目录路径
- **版本兼容**：支持程序升级后的状态兼容

### 🔧 用户友好
- **无感知操作**：所有功能都在后台自动运行
- **性能优化**：不影响程序启动和运行速度
- **错误处理**：完善的异常处理，确保功能稳定

## 使用方法

### 基本使用

1. **正常使用目录树**
   - 像往常一样展开和折叠目录
   - 双击目录切换工作目录
   - 使用右键菜单进行操作

2. **自动记忆**
   - 程序会自动记录你展开的目录
   - 无需任何手动操作
   - 状态实时更新

3. **自动恢复**
   - 下次启动程序时
   - 之前展开的目录会自动恢复
   - 保持你熟悉的界面状态

### 高级功能

#### 控制按钮
- **刷新**：重新加载目录树，清除当前记忆
- **展开**：展开所有目录（会更新记忆状态）
- **折叠**：折叠所有目录（会更新记忆状态）

#### 右键菜单
- **设为当前目录**：快速设置工作目录
- **在文件管理器中打开**：使用系统文件管理器打开
- **刷新**：刷新选中的目录

## 状态文件

### 文件位置
记忆状态保存在用户主目录的隐藏文件中：
```
Windows: C:\Users\<USER>\.duplicate_finder_tree_state.json
Linux/macOS: /home/<USER>/.duplicate_finder_tree_state.json
```

### 文件格式
```json
{
  "expanded_directories": [
    "C:\\Users\\<USER>\\Documents",
    "C:\\Users\\<USER>\\Documents\\Projects",
    "C:\\Users\\<USER>\\Downloads"
  ],
  "current_directory": "C:\\Users\\<USER>\\Documents\\Projects",
  "last_updated": 1703123456.789
}
```

### 手动管理
- **查看状态**：可以打开状态文件查看记录的目录
- **清除记忆**：删除状态文件可清除所有记忆
- **备份恢复**：可以备份状态文件到其他设备

## 常见问题

### Q: 为什么有些目录没有恢复？
A: 可能的原因：
- 目录已被删除或移动
- 没有访问权限
- 状态文件已过期（超过7天）
- 程序异常退出时未保存状态

### Q: 如何清除所有记忆状态？
A: 有几种方法：
1. 删除状态文件（推荐）
2. 使用"刷新"按钮重新初始化
3. 手动折叠所有目录后退出程序

### Q: 记忆功能会影响程序性能吗？
A: 不会，记忆功能经过优化：
- 使用懒加载策略
- 后台异步处理
- 最小化内存占用
- 不影响程序启动速度

### Q: 可以在不同电脑间同步记忆状态吗？
A: 可以手动同步：
1. 复制状态文件到其他电脑
2. 放在相同的位置
3. 确保目录路径在新电脑上存在

### Q: 状态文件损坏了怎么办？
A: 程序会自动处理：
- 检测到损坏文件会自动忽略
- 使用默认状态重新开始
- 不会影响程序正常运行

## 技术细节

### 记忆触发时机
- **展开目录时**：立即更新记忆状态
- **折叠目录时**：立即更新记忆状态
- **程序退出时**：保存最终状态到文件
- **切换目录时**：更新当前工作目录

### 恢复策略
- **启动时加载**：程序启动后100ms开始恢复
- **按深度排序**：先恢复浅层目录，再恢复深层目录
- **路径验证**：只恢复仍然存在的目录
- **静默恢复**：不选中目录，只展开路径

### 性能优化
- **批量处理**：一次性处理多个目录
- **异步执行**：不阻塞主界面
- **内存控制**：限制记忆的目录数量
- **定期清理**：自动清理无效记录

## 故障排除

### 记忆功能不工作
1. 检查状态文件是否存在
2. 确认文件权限是否正确
3. 查看程序是否正常退出
4. 检查目录路径是否仍然有效

### 恢复速度慢
1. 减少展开的目录数量
2. 检查网络驱动器连接
3. 确认磁盘空间充足
4. 重启程序清理缓存

### 状态文件过大
1. 定期清理不需要的展开状态
2. 删除状态文件重新开始
3. 避免展开过多深层目录
4. 使用折叠功能管理展开状态

## 最佳实践

### 日常使用建议
1. **适度展开**：只展开常用的目录，避免展开过多
2. **定期清理**：偶尔使用"折叠"功能清理不需要的展开状态
3. **合理组织**：将常用目录组织在较浅的层级
4. **备份重要状态**：对于重要的目录结构可以备份状态文件

### 性能优化建议
1. **避免深层展开**：尽量不要展开过深的目录层级
2. **网络驱动器**：网络驱动器的展开可能较慢
3. **大量目录**：包含大量子目录的文件夹展开会较慢
4. **定期重启**：偶尔重启程序可以清理内存缓存

## 更新日志

### v1.0 - 基础功能
- ✅ 自动保存和恢复展开状态
- ✅ 7天有效期管理
- ✅ 跨平台路径支持
- ✅ 异常处理和错误恢复

### 未来计划
- 🔄 云同步支持
- 🔄 更多自定义选项
- 🔄 性能进一步优化
- 🔄 可视化状态管理界面

## 总结

目录树记忆功能让重复文件查找器更加智能和易用：

- **提升效率**：无需重复展开常用目录
- **保持习惯**：维持你熟悉的界面布局
- **智能管理**：自动处理过期和无效状态
- **用户友好**：完全自动化，无需手动操作

这个功能特别适合：
- 经常使用相同目录结构的用户
- 需要频繁切换多个项目目录的开发者
- 有复杂目录层级的文件管理需求
- 希望提高工作效率的所有用户

享受更智能的目录浏览体验吧！ 🎉
