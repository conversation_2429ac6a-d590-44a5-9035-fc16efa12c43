"""
测试左侧目录树功能
"""
import os
import tempfile
import shutil
import tkinter as tk
from tkinter import messagebox
import time


def create_test_directory_structure():
    """创建测试目录结构"""
    base_dir = tempfile.mkdtemp(prefix="directory_tree_test_")
    print(f"创建测试基础目录: {base_dir}")
    
    # 创建多层目录结构
    test_structure = {
        "文档": {
            "工作文档": ["report1.txt", "report2.txt"],
            "个人文档": ["diary.txt", "notes.txt"]
        },
        "图片": {
            "风景": ["sunset.jpg", "mountain.jpg"],
            "人物": ["family.jpg", "friends.jpg"]
        },
        "视频": {
            "电影": ["movie1.mp4", "movie2.mp4"],
            "短视频": ["clip1.mp4", "clip2.mp4"]
        }
    }
    
    created_dirs = []
    created_files = []
    
    def create_structure(base_path, structure):
        for name, content in structure.items():
            dir_path = os.path.join(base_path, name)
            os.makedirs(dir_path, exist_ok=True)
            created_dirs.append(dir_path)
            
            if isinstance(content, dict):
                # 递归创建子目录
                create_structure(dir_path, content)
            elif isinstance(content, list):
                # 创建文件
                for filename in content:
                    file_path = os.path.join(dir_path, filename)
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(f"这是测试文件: {filename}")
                    created_files.append(file_path)
    
    create_structure(base_dir, test_structure)
    
    print(f"创建了 {len(created_dirs)} 个目录和 {len(created_files)} 个文件")
    return base_dir, created_dirs, created_files


def test_directory_tree():
    """测试目录树功能"""
    print("开始测试目录树功能...")
    
    # 创建测试目录结构
    test_base_dir, test_dirs, test_files = create_test_directory_structure()
    
    try:
        # 导入主程序
        import sys
        sys.path.append('.')
        
        # 创建主窗口
        root = tk.Tk()
        root.title("目录树功能测试")
        root.geometry("1200x800")
        
        # 导入并创建应用实例
        from 重复文件查找器 import FileSearchApp
        app = FileSearchApp(root)
        
        print("1. 检查目录树组件是否创建...")
        
        # 检查目录树组件是否存在
        if not hasattr(app, 'directory_tree'):
            print("❌ 目录树组件未创建")
            return False
        
        if not hasattr(app, 'directory_frame'):
            print("❌ 目录框架未创建")
            return False
        
        print("✓ 目录树组件创建成功")
        
        print("2. 检查目录树是否正确初始化...")
        
        # 检查是否有根节点
        root_items = app.directory_tree.get_children()
        if not root_items:
            print("❌ 目录树没有根节点")
            return False
        
        print(f"✓ 目录树有 {len(root_items)} 个根节点")
        
        print("3. 测试目录展开功能...")
        
        # 模拟测试过程
        def simulate_test():
            try:
                # 测试展开到测试目录
                print(f"展开到测试目录: {test_base_dir}")
                app.expand_to_directory(test_base_dir)
                root.update()
                
                # 检查是否正确展开
                selection = app.directory_tree.selection()
                if selection:
                    selected_item = selection[0]
                    selected_tags = app.directory_tree.item(selected_item, "tags")
                    if selected_tags and selected_tags[0] == test_base_dir:
                        print("✓ 成功展开到测试目录")
                    else:
                        print(f"❌ 展开目录不正确: {selected_tags}")
                        return False
                else:
                    print("❌ 没有选中任何目录")
                    return False
                
                print("4. 测试目录选择功能...")
                
                # 测试选择子目录
                test_subdir = test_dirs[0]  # 选择第一个子目录
                print(f"测试选择子目录: {test_subdir}")
                
                app.expand_to_directory(test_subdir)
                root.update()
                
                # 检查选择结果
                selection = app.directory_tree.selection()
                if selection:
                    selected_item = selection[0]
                    selected_tags = app.directory_tree.item(selected_item, "tags")
                    if selected_tags and selected_tags[0] == test_subdir:
                        print("✓ 成功选择子目录")
                    else:
                        print(f"❌ 选择子目录不正确: {selected_tags}")
                        return False
                
                print("5. 测试双击切换目录功能...")
                
                # 模拟双击事件
                original_current_dir = app.current_directory
                
                # 直接调用双击处理方法
                app.directory_tree.selection_set(selected_item)
                
                # 创建模拟事件
                class MockEvent:
                    def __init__(self):
                        pass
                
                mock_event = MockEvent()
                app.on_directory_double_click(mock_event)
                root.update()
                
                # 检查当前目录是否改变
                if app.current_directory == test_subdir:
                    print("✓ 双击切换目录成功")
                else:
                    print(f"❌ 双击切换目录失败: {app.current_directory} != {test_subdir}")
                    return False
                
                # 检查界面状态
                path_text = app.path_var.get()
                if test_subdir in path_text:
                    print("✓ 路径显示更新正确")
                else:
                    print(f"❌ 路径显示更新错误: {path_text}")
                    return False
                
                # 检查搜索按钮状态
                if app.search_by_size_btn.cget('state') == 'normal':
                    print("✓ 搜索按钮已启用")
                else:
                    print("❌ 搜索按钮未启用")
                    return False
                
                print("6. 测试目录树控制按钮...")
                
                # 测试刷新按钮
                if hasattr(app, 'refresh_tree_btn'):
                    print("✓ 刷新按钮存在")
                    # 可以测试点击刷新按钮
                    app.refresh_directory_tree()
                    root.update()
                    print("✓ 刷新功能正常")
                else:
                    print("❌ 刷新按钮不存在")
                    return False
                
                # 测试展开/折叠按钮
                if hasattr(app, 'expand_all_btn') and hasattr(app, 'collapse_all_btn'):
                    print("✓ 展开/折叠按钮存在")
                    
                    # 测试折叠所有
                    app.collapse_all_directories()
                    root.update()
                    print("✓ 折叠功能正常")
                    
                    # 测试展开所有（注意：这可能很慢，所以只测试存在性）
                    print("✓ 展开功能存在")
                else:
                    print("❌ 展开/折叠按钮不存在")
                    return False
                
                print("7. 测试界面布局...")
                
                # 检查三栏布局
                paned_children = app.paned_window.panes()
                if len(paned_children) >= 3:
                    print(f"✓ 三栏布局正确，有 {len(paned_children)} 个面板")
                else:
                    print(f"❌ 布局不正确，只有 {len(paned_children)} 个面板")
                    return False
                
                # 检查目录树是否可见
                if app.directory_frame.winfo_viewable():
                    print("✓ 目录树面板可见")
                else:
                    print("❌ 目录树面板不可见")
                    return False
                
                print("8. 所有测试通过！")
                return True
                
            except Exception as e:
                print(f"❌ 测试过程中出错: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        # 在主线程中执行测试
        root.after(500, lambda: test_and_exit(simulate_test, root))
        
        # 运行主循环
        root.mainloop()
        
    finally:
        # 清理测试目录
        try:
            shutil.rmtree(test_base_dir)
            print(f"清理测试目录: {test_base_dir}")
        except Exception as e:
            print(f"清理测试目录失败: {e}")


def test_and_exit(test_func, root):
    """执行测试并退出"""
    try:
        success = test_func()
        if success:
            print("\n🎉 所有测试通过！目录树功能正常工作。")
            print("✓ 目录树组件创建成功")
            print("✓ 目录展开和选择功能正常")
            print("✓ 双击切换目录功能正常")
            print("✓ 控制按钮功能正常")
            print("✓ 三栏布局正确")
        else:
            print("\n❌ 测试失败！需要进一步检查实现。")
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
    finally:
        # 延迟关闭，让用户看到结果
        root.after(3000, root.quit)


def main():
    """主函数"""
    print("=" * 50)
    print("目录树功能测试")
    print("=" * 50)
    
    test_directory_tree()


if __name__ == "__main__":
    main()
