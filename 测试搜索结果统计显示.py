"""
测试搜索结果统计信息显示功能
验证统计信息是否正确显示在标题旁边
"""
import os
import tempfile
import shutil
import tkinter as tk
import time


def create_test_files_for_stats():
    """创建用于测试统计显示的文件"""
    base_dir = tempfile.mkdtemp(prefix="stats_test_")
    print(f"创建测试目录: {base_dir}")
    
    # 创建多组相同大小的文件
    test_groups = [
        # 组1: 100字节文件
        ("group1_file1.txt", "A" * 100),
        ("group1_file2.txt", "B" * 100),
        ("group1_file3.txt", "C" * 100),
        
        # 组2: 200字节文件
        ("group2_file1.txt", "D" * 200),
        ("group2_file2.txt", "E" * 200),
        
        # 组3: 300字节文件
        ("group3_file1.txt", "F" * 300),
        ("group3_file2.txt", "G" * 300),
        ("group3_file3.txt", "H" * 300),
        ("group3_file4.txt", "I" * 300),
        
        # 组4: 50字节文件
        ("group4_file1.txt", "J" * 50),
        ("group4_file2.txt", "K" * 50),
        
        # 单独文件（不会形成组）
        ("unique_file.txt", "UNIQUE" * 10)
    ]
    
    for filename, content in test_groups:
        file_path = os.path.join(base_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"创建文件: {filename} ({len(content)} 字节)")
    
    return base_dir


def test_search_result_stats_display():
    """测试搜索结果统计信息显示"""
    print("=" * 60)
    print("搜索结果统计信息显示测试")
    print("=" * 60)
    
    # 创建测试文件
    test_dir = create_test_files_for_stats()
    
    try:
        # 导入主程序
        import sys
        sys.path.append('.')
        
        # 创建主窗口
        root = tk.Tk()
        root.title("搜索结果统计显示测试")
        root.geometry("1400x800")
        
        # 导入并创建应用实例
        from 重复文件查找器 import FileSearchApp
        app = FileSearchApp(root)
        
        print("1. 检查统计标签是否存在...")
        
        # 检查新增的统计标签
        stats_components = [
            ('result_stats_var', '统计信息变量'),
            ('result_stats_label', '统计信息标签')
        ]
        
        all_components_exist = True
        for attr_name, display_name in stats_components:
            if hasattr(app, attr_name):
                print(f"✓ {display_name}存在")
            else:
                print(f"❌ {display_name}不存在")
                all_components_exist = False
        
        if not all_components_exist:
            print("❌ 统计显示组件缺失")
            return False
        
        print("2. 检查初始状态...")
        
        # 检查初始状态
        initial_stats = app.result_stats_var.get()
        print(f"初始统计信息: '{initial_stats}'")
        
        if initial_stats == "":
            print("✓ 初始统计信息为空")
        else:
            print(f"❌ 初始统计信息不为空: {initial_stats}")
            return False
        
        print("3. 设置测试目录并进行搜索...")
        
        # 设置测试目录
        app.current_directory = test_dir
        app.path_var.set(f"当前文件夹: {test_dir}")
        app.search_by_size_btn.configure(state='normal')
        app.search_by_duration_btn.configure(state='normal')
        
        # 等待界面更新
        root.update()
        time.sleep(0.2)
        
        print("4. 模拟搜索相同大小文件...")
        
        # 模拟搜索结果
        mock_results = {}
        
        # 收集所有文件并按大小分组
        for filename in os.listdir(test_dir):
            file_path = os.path.join(test_dir, filename)
            if os.path.isfile(file_path):
                file_size = os.path.getsize(file_path)
                if file_size not in mock_results:
                    mock_results[file_size] = []
                mock_results[file_size].append(file_path)
        
        # 只保留有多个文件的组
        duplicate_groups = {}
        for size, files in mock_results.items():
            if len(files) > 1:
                duplicate_groups[size] = files
        
        print(f"模拟找到 {len(duplicate_groups)} 个重复文件组")
        
        # 设置搜索结果
        app.current_results = duplicate_groups
        app.current_search_type = "size"
        
        # 模拟显示结果（调用display_header_info）
        app.display_header_info(duplicate_groups)
        root.update()
        
        print("5. 检查统计信息是否正确显示...")
        
        # 检查统计标签内容
        stats_text = app.result_stats_var.get()
        print(f"显示的统计信息: '{stats_text}'")
        
        expected_count = len(duplicate_groups)
        expected_text = f"找到 {expected_count} 个大小相同的文件组"
        
        if stats_text == expected_text:
            print("✓ 统计信息显示正确")
        else:
            print(f"❌ 统计信息显示错误")
            print(f"  期望: {expected_text}")
            print(f"  实际: {stats_text}")
            return False
        
        print("6. 测试时长搜索的统计显示...")
        
        # 模拟时长搜索
        app.current_search_type = "duration"
        app.display_header_info(duplicate_groups)
        root.update()
        
        # 检查时长搜索的统计信息
        duration_stats_text = app.result_stats_var.get()
        expected_duration_text = f"找到 {expected_count} 个时长相同的文件组"
        
        if duration_stats_text == expected_duration_text:
            print("✓ 时长搜索统计信息显示正确")
        else:
            print(f"❌ 时长搜索统计信息显示错误")
            print(f"  期望: {expected_duration_text}")
            print(f"  实际: {duration_stats_text}")
            return False
        
        print("7. 测试清空时的统计信息...")
        
        # 模拟清空结果
        app.text_area.delete(1.0, tk.END)
        app.text_area.insert(tk.END, "请选择搜索方式：相同大小 或 相同时长\n")
        app.result_stats_var.set("")  # 模拟清空统计
        root.update()
        
        # 检查统计信息是否被清空
        cleared_stats = app.result_stats_var.get()
        if cleared_stats == "":
            print("✓ 清空时统计信息正确清除")
        else:
            print(f"❌ 清空时统计信息未清除: '{cleared_stats}'")
            return False
        
        print("8. 测试界面布局...")
        
        # 检查统计标签是否可见
        try:
            if app.result_stats_label.winfo_viewable():
                print("✓ 统计标签可见")
            else:
                print("❌ 统计标签不可见")
                return False
        except Exception as e:
            print(f"❌ 检查统计标签可见性失败: {e}")
            return False
        
        # 检查标签位置（应该在右侧）
        try:
            label_info = app.result_stats_label.pack_info()
            if label_info.get('side') == 'right':
                print("✓ 统计标签位置正确（右侧）")
            else:
                print(f"❌ 统计标签位置错误: {label_info.get('side')}")
                return False
        except Exception as e:
            print(f"❌ 检查统计标签位置失败: {e}")
            return False
        
        print("9. 所有测试通过！")
        
        # 关闭窗口
        root.after(2000, root.quit)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理测试目录
        try:
            shutil.rmtree(test_dir)
            print(f"清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理测试目录失败: {e}")


def main():
    """主函数"""
    try:
        success = test_search_result_stats_display()
        
        if success:
            print("\n🎉 搜索结果统计信息显示功能测试通过！")
            print("✓ 统计标签创建成功")
            print("✓ 统计信息显示正确")
            print("✓ 支持大小和时长两种搜索类型")
            print("✓ 清空功能正常")
            print("✓ 界面布局合理")
            print("\n功能改进总结:")
            print("• 统计信息从结果列表中移到标题旁边")
            print("• 用户可以更方便地查看搜索结果概况")
            print("• 界面更加简洁，信息层次更清晰")
            print("• 支持实时更新统计信息")
            print("• 与现有功能完美集成")
        else:
            print("\n❌ 测试失败！需要进一步检查实现。")
            
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
