"""
验证文件夹记忆功能修复
"""
import os
import tempfile
import shutil
import tkinter as tk
import json
import time


def test_folder_memory_fix():
    """测试文件夹记忆功能修复"""
    print("=" * 50)
    print("验证文件夹记忆功能修复")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="memory_fix_test_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 导入主程序
        import sys
        sys.path.append('.')
        
        from 重复文件查找器 import FileSearchApp
        
        print("1. 测试程序初始化...")
        
        # 创建主窗口
        root = tk.Tk()
        root.title("文件夹记忆修复验证")
        root.geometry("800x600")
        
        # 创建应用实例
        app = FileSearchApp(root)
        
        print("✓ 程序初始化成功，没有属性错误")
        
        print("2. 检查相关属性是否正确初始化...")
        
        # 检查关键属性
        attributes_to_check = [
            ('current_directory', '当前目录'),
            ('last_directory', '上次目录'),
            ('config_file', '配置文件路径'),
            ('directory_tree_state_file', '目录树状态文件路径'),
            ('expanded_directories', '展开目录集合')
        ]
        
        all_attributes_ok = True
        for attr_name, display_name in attributes_to_check:
            if hasattr(app, attr_name):
                attr_value = getattr(app, attr_name)
                print(f"✓ {display_name}: {attr_value}")
            else:
                print(f"❌ {display_name}不存在")
                all_attributes_ok = False
        
        if not all_attributes_ok:
            print("❌ 部分属性缺失")
            return False
        
        print("3. 测试配置保存和加载...")
        
        # 设置测试目录
        app.current_directory = test_dir
        app.save_config()
        
        # 检查配置文件是否创建
        config_file = app.config_file
        if os.path.exists(config_file):
            print("✓ 配置文件创建成功")
            
            # 读取配置内容
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            saved_directory = config_data.get('last_directory', '')
            if saved_directory == test_dir:
                print("✓ 目录保存正确")
            else:
                print(f"❌ 目录保存错误: {saved_directory} != {test_dir}")
                return False
        else:
            print("❌ 配置文件未创建")
            return False
        
        print("4. 测试select_directory方法的初始目录逻辑...")
        
        # 检查select_directory方法中的初始目录选择逻辑
        # 我们不能直接测试文件对话框，但可以检查相关属性和逻辑
        
        # 模拟有last_directory的情况
        app.last_directory = test_dir
        app.current_directory = ""
        
        # 检查初始目录选择逻辑（模拟select_directory中的逻辑）
        initial_dir = ""
        if app.last_directory and os.path.exists(app.last_directory):
            initial_dir = app.last_directory
        elif app.current_directory and os.path.exists(app.current_directory):
            initial_dir = app.current_directory
        else:
            initial_dir = os.path.expanduser("~")
        
        if initial_dir == test_dir:
            print("✓ 初始目录选择逻辑正确")
        else:
            print(f"❌ 初始目录选择逻辑错误: {initial_dir} != {test_dir}")
            return False
        
        print("5. 测试restore_last_directory方法...")
        
        # 重置状态
        app.current_directory = ""
        app.path_var.set("")
        app.search_by_size_btn.configure(state='disabled')
        app.search_by_duration_btn.configure(state='disabled')
        
        # 设置last_directory
        app.last_directory = test_dir
        
        # 调用恢复方法
        app.restore_last_directory()
        root.update()

        # 等待一下，让所有异步操作完成
        time.sleep(0.2)
        root.update()

        # 检查恢复结果
        if app.current_directory == test_dir:
            print("✓ 目录恢复正确")
        else:
            print(f"❌ 目录恢复错误: {app.current_directory} != {test_dir}")
            return False

        # 检查界面状态
        path_text = app.path_var.get()
        if test_dir in path_text:
            print("✓ 路径显示更新正确")
        else:
            print(f"❌ 路径显示更新错误: {path_text}")
            return False

        # 检查按钮状态（多次检查确保稳定）
        button_states = []
        for i in range(3):
            time.sleep(0.1)
            root.update()
            state = app.search_by_size_btn.cget('state')
            button_states.append(str(state))  # 转换为字符串
            print(f"按钮状态检查 {i+1}: {state}")

        if 'normal' in button_states:
            print("✓ 搜索按钮已启用")
        else:
            print(f"❌ 搜索按钮未启用，状态历史: {button_states}")
            return False
        
        print("6. 测试无效目录处理...")
        
        # 设置无效目录
        invalid_dir = os.path.join(test_dir, "不存在的目录")
        app.last_directory = invalid_dir
        app.current_directory = ""
        app.path_var.set("")
        app.search_by_size_btn.configure(state='disabled')
        
        # 调用恢复方法
        app.restore_last_directory()
        root.update()
        
        # 检查是否正确处理无效目录
        if not app.current_directory:
            print("✓ 无效目录处理正确")
        else:
            print(f"❌ 无效目录处理错误: {app.current_directory}")
            return False
        
        print("7. 所有测试通过！")
        
        # 关闭窗口
        root.after(1000, root.quit)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理测试目录
        try:
            shutil.rmtree(test_dir)
            print(f"清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理测试目录失败: {e}")


def main():
    """主函数"""
    try:
        success = test_folder_memory_fix()
        
        if success:
            print("\n🎉 文件夹记忆功能修复验证成功！")
            print("✓ 程序初始化正常，无属性错误")
            print("✓ 配置保存和加载功能正常")
            print("✓ 初始目录选择逻辑正确")
            print("✓ 目录恢复功能正常")
            print("✓ 无效目录处理正确")
            print("\n功能特点:")
            print("• 程序启动时自动恢复上次选择的目录")
            print("• 文件夹选择对话框使用上次目录作为初始位置")
            print("• 配置持久化保存，重启程序后仍然有效")
            print("• 智能处理无效或不存在的目录")
            print("• 与目录树功能完美集成")
        else:
            print("\n❌ 验证失败！需要进一步检查。")
            
    except Exception as e:
        print(f"验证过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
