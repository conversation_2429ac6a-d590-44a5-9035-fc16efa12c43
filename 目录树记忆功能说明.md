# 目录树记忆功能实现说明

## 功能概述

为重复文件查找器的目录树添加了记忆功能，能够记住用户之前展开的目录状态，在下次启动程序时自动恢复这些展开状态，提供更好的用户体验。

## 主要特性

### 1. 状态持久化
- **自动保存**：程序退出时自动保存目录树的展开状态
- **自动恢复**：程序启动时自动恢复之前的展开状态
- **文件存储**：状态信息保存在用户主目录的隐藏文件中

### 2. 智能管理
- **时效性控制**：状态文件有7天的有效期，过期自动忽略
- **实时更新**：用户展开/折叠目录时实时更新记忆状态
- **路径验证**：恢复时验证目录是否仍然存在

### 3. 用户体验优化
- **无感知操作**：记忆功能完全在后台运行，用户无需手动操作
- **性能优化**：使用懒加载和批量处理，不影响程序启动速度
- **错误处理**：完善的异常处理，确保功能稳定

## 技术实现

### 1. 数据结构

#### 状态记录
```python
# 内存中的展开目录集合
self.expanded_directories = set()  # 记录展开的目录路径

# 状态文件路径
self.directory_tree_state_file = os.path.join(
    os.path.expanduser("~"), 
    ".duplicate_finder_tree_state.json"
)
```

#### 状态文件格式
```json
{
  "expanded_directories": [
    "C:\\Users\\<USER>\\Documents",
    "C:\\Users\\<USER>\\Documents\\Projects",
    "C:\\Users\\<USER>\\Downloads"
  ],
  "current_directory": "C:\\Users\\<USER>\\Documents\\Projects",
  "last_updated": 1703123456.789
}
```

### 2. 核心方法

#### 保存状态
```python
def save_directory_tree_state(self):
    """保存目录树状态"""
    # 收集展开的目录路径
    expanded_paths = []
    
    def collect_expanded_items(parent=""):
        for item in self.directory_tree.get_children(parent):
            if self.directory_tree.item(item, "open"):
                tags = self.directory_tree.item(item, "tags")
                if tags:
                    expanded_paths.append(tags[0])
            collect_expanded_items(item)  # 递归收集
    
    collect_expanded_items()
    
    # 保存到文件
    state_data = {
        "expanded_directories": expanded_paths,
        "current_directory": self.current_directory,
        "last_updated": time.time()
    }
    
    with open(self.directory_tree_state_file, 'w', encoding='utf-8') as f:
        json.dump(state_data, f, ensure_ascii=False, indent=2)
```

#### 加载状态
```python
def load_directory_tree_state(self):
    """加载目录树状态"""
    if not os.path.exists(self.directory_tree_state_file):
        return
    
    with open(self.directory_tree_state_file, 'r', encoding='utf-8') as f:
        state_data = json.load(f)
    
    # 检查时效性（7天内有效）
    last_updated = state_data.get("last_updated", 0)
    if time.time() - last_updated > 7 * 24 * 3600:
        return  # 过期，不恢复
    
    # 恢复展开状态
    expanded_directories = state_data.get("expanded_directories", [])
    self.expanded_directories = set(expanded_directories)
    
    # 延迟恢复，确保目录树已初始化
    self.root.after(100, self.restore_expanded_directories)
```

#### 恢复展开状态
```python
def restore_expanded_directories(self):
    """恢复展开的目录"""
    if not self.expanded_directories:
        return
    
    # 按路径深度排序，先展开浅层目录
    sorted_paths = sorted(self.expanded_directories, key=lambda x: x.count(os.sep))
    
    for directory_path in sorted_paths:
        if os.path.exists(directory_path):
            self.expand_to_directory_silent(directory_path)
        else:
            # 移除不存在的目录
            self.expanded_directories.discard(directory_path)
```

#### 实时状态更新
```python
def update_expanded_directories_state(self):
    """更新展开目录状态记录"""
    current_expanded = set()
    
    def collect_expanded_items(parent=""):
        for item in self.directory_tree.get_children(parent):
            if self.directory_tree.item(item, "open"):
                tags = self.directory_tree.item(item, "tags")
                if tags:
                    current_expanded.add(tags[0])
            collect_expanded_items(item)
    
    collect_expanded_items()
    self.expanded_directories = current_expanded
```

### 3. 事件绑定

#### 目录树事件监听
```python
# 绑定展开/折叠事件
self.directory_tree.bind("<<TreeviewOpen>>", self.on_directory_expand)
self.directory_tree.bind("<<TreeviewClose>>", self.on_directory_collapse)

def on_directory_expand(self, event):
    """目录树展开事件"""
    self.update_expanded_directories_state()

def on_directory_collapse(self, event):
    """目录树折叠事件"""
    self.update_expanded_directories_state()
```

#### 程序退出时保存
```python
def on_closing(self):
    try:
        # 保存目录树状态
        self.save_directory_tree_state()
        # 其他清理工作...
    except Exception as e:
        print(f"程序退出时出错: {e}")
    finally:
        self.root.destroy()
```

### 4. 静默展开功能

为了恢复状态时不干扰用户，实现了静默展开功能：

```python
def expand_to_directory_silent(self, target_path):
    """静默展开到指定目录（不选中，用于恢复状态）"""
    # 分解路径并逐级展开
    # 不选中目标目录，只展开路径
    # 返回是否成功展开
```

## 使用流程

### 1. 程序启动
1. 创建目录树组件
2. 初始化基本目录结构
3. 加载保存的状态文件
4. 验证状态文件时效性
5. 恢复展开状态

### 2. 用户操作
1. 用户展开/折叠目录
2. 触发相应事件处理
3. 实时更新内存中的状态记录
4. 状态变化立即生效

### 3. 程序退出
1. 收集当前展开状态
2. 保存到状态文件
3. 包含时间戳和当前目录信息
4. 确保数据完整性

## 性能优化

### 1. 懒加载策略
- 状态恢复使用延迟执行，不阻塞程序启动
- 按路径深度排序，优先恢复浅层目录
- 批量处理，减少界面更新次数

### 2. 内存管理
- 使用集合(set)存储路径，自动去重
- 及时清理不存在的路径
- 限制状态文件大小

### 3. 错误处理
- 完善的异常捕获和处理
- 文件操作失败时的回退机制
- 状态文件损坏时的恢复策略

## 配置选项

### 1. 状态文件位置
```python
# 默认位置：用户主目录
self.directory_tree_state_file = os.path.join(
    os.path.expanduser("~"), 
    ".duplicate_finder_tree_state.json"
)
```

### 2. 有效期设置
```python
# 7天有效期
if time.time() - last_updated > 7 * 24 * 3600:
    return  # 过期处理
```

### 3. 延迟恢复时间
```python
# 100毫秒延迟，确保界面初始化完成
self.root.after(100, self.restore_expanded_directories)
```

## 兼容性考虑

### 1. 跨平台路径处理
- 使用 `os.path` 模块处理路径分隔符
- 支持Windows和Unix系统的路径格式
- 处理特殊字符和长路径

### 2. 文件编码
- 使用UTF-8编码保存状态文件
- 支持包含中文的路径名
- 确保跨系统兼容性

### 3. 版本兼容
- JSON格式便于扩展和版本升级
- 向后兼容的数据结构设计
- 优雅的降级处理

## 用户体验

### 1. 无感知操作
- 用户无需手动保存或恢复状态
- 所有操作在后台自动完成
- 不影响正常的目录浏览体验

### 2. 智能恢复
- 只恢复仍然存在的目录
- 自动清理无效的状态记录
- 保持界面的整洁和准确

### 3. 性能友好
- 不影响程序启动速度
- 状态恢复过程流畅自然
- 内存占用控制在合理范围

## 故障排除

### 1. 状态文件问题
- 文件不存在：正常情况，使用默认状态
- 文件损坏：忽略并重新创建
- 权限问题：降级到内存模式

### 2. 路径问题
- 目录不存在：自动从记录中移除
- 权限不足：跳过该目录
- 路径格式错误：使用异常处理

### 3. 性能问题
- 展开目录过多：限制最大数量
- 恢复时间过长：分批处理
- 内存占用过高：定期清理

## 总结

目录树记忆功能的实现显著提升了用户体验：

1. **便利性**：无需重复展开常用目录
2. **智能性**：自动管理状态的有效性
3. **稳定性**：完善的错误处理机制
4. **性能**：优化的加载和恢复策略

这个功能让用户能够更高效地使用目录树，特别适合需要频繁访问深层目录结构的使用场景。
