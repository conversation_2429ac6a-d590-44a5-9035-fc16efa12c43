"""
简单验证停止预览功能是否正确添加到各个操作中
"""
import os
import sys
sys.path.append('.')

def check_stop_preview_calls():
    """检查各个方法中是否正确添加了停止预览的调用"""
    print("=" * 60)
    print("验证停止预览功能的代码实现")
    print("=" * 60)
    
    # 读取源代码文件
    with open('重复文件查找器.py', 'r', encoding='utf-8') as f:
        source_code = f.read()
    
    # 需要检查的方法和它们应该包含的停止预览调用
    methods_to_check = [
        ('def select_directory(self):', '切换文件夹时停止当前预览'),
        ('def search_by_size(self):', '开始新搜索时停止当前预览'),
        ('def search_by_duration(self):', '开始新搜索时停止当前预览'),
        ('def rename_file(self, file_path, size_key):', '重命名操作前停止当前预览'),
        ('def load_results(self):', '加载结果前停止当前预览'),
        ('def set_current_directory_from_tree(self, directory_path):', '从目录树切换目录时停止当前预览')
    ]
    
    print("1. 检查各个方法中是否添加了停止预览调用...")
    
    all_methods_ok = True
    
    for method_signature, description in methods_to_check:
        print(f"\n检查方法: {method_signature}")
        
        # 找到方法的开始位置
        method_start = source_code.find(method_signature)
        if method_start == -1:
            print(f"❌ 未找到方法: {method_signature}")
            all_methods_ok = False
            continue
        
        # 找到下一个方法的开始位置（作为当前方法的结束）
        next_method_start = source_code.find('\n    def ', method_start + 1)
        if next_method_start == -1:
            # 如果没有下一个方法，使用文件结尾
            method_code = source_code[method_start:]
        else:
            method_code = source_code[method_start:next_method_start]
        
        # 检查是否包含停止预览的调用
        if 'self.stop_preview()' in method_code:
            print(f"✓ 已添加停止预览调用")
            
            # 查找具体的注释
            lines = method_code.split('\n')
            for line in lines:
                if 'stop_preview()' in line and '#' in line:
                    comment = line.split('#')[1].strip()
                    print(f"  注释: {comment}")
                    break
        else:
            print(f"❌ 未添加停止预览调用")
            all_methods_ok = False
    
    print("\n2. 检查已有的停止预览调用...")
    
    # 检查已有的停止预览调用
    existing_calls = [
        'delete_file_from_disk',
        'delete_selected_files', 
        'remove_selected_from_list',
        'remove_file_from_list'
    ]
    
    for method_name in existing_calls:
        if f'def {method_name}(' in source_code:
            method_start = source_code.find(f'def {method_name}(')
            next_method_start = source_code.find('\n    def ', method_start + 1)
            if next_method_start == -1:
                method_code = source_code[method_start:]
            else:
                method_code = source_code[method_start:next_method_start]
            
            if 'self.stop_preview()' in method_code:
                print(f"✓ {method_name} 已包含停止预览调用")
            else:
                print(f"❌ {method_name} 缺少停止预览调用")
                all_methods_ok = False
    
    print("\n3. 检查停止预览方法本身...")
    
    # 检查stop_preview方法的实现
    if 'def stop_preview(self):' in source_code:
        print("✓ stop_preview方法存在")
        
        stop_preview_start = source_code.find('def stop_preview(self):')
        next_method_start = source_code.find('\n    def ', stop_preview_start + 1)
        if next_method_start == -1:
            stop_preview_code = source_code[stop_preview_start:]
        else:
            stop_preview_code = source_code[stop_preview_start:next_method_start]
        
        # 检查关键功能
        required_actions = [
            ('self.stop_video_playback()', '停止视频播放'),
            ('self._current_preview = None', '清除当前预览文件'),
            ('self.preview_filename_var.set("")', '清除预览文件名'),
            ('self.stop_preview_btn.configure(state=\'disabled\')', '禁用停止按钮'),
            ('self._is_processing = False', '重置处理标志')
        ]
        
        for action, description in required_actions:
            if action in stop_preview_code:
                print(f"✓ {description}")
            else:
                print(f"❌ 缺少: {description}")
                all_methods_ok = False
    else:
        print("❌ stop_preview方法不存在")
        all_methods_ok = False
    
    print("\n4. 统计停止预览调用的总数...")
    
    # 统计所有的停止预览调用
    stop_preview_calls = source_code.count('self.stop_preview()')
    print(f"总共找到 {stop_preview_calls} 个停止预览调用")
    
    # 列出所有调用的上下文
    lines = source_code.split('\n')
    call_contexts = []
    
    for i, line in enumerate(lines):
        if 'self.stop_preview()' in line:
            # 获取上下文（前面几行的注释或方法名）
            context = ""
            for j in range(max(0, i-5), i):
                if 'def ' in lines[j] and lines[j].strip().startswith('def '):
                    context = lines[j].strip()
                    break
                elif '#' in lines[j] and '停止' in lines[j]:
                    context = lines[j].strip()
            
            call_contexts.append((i+1, context, line.strip()))
    
    print("\n停止预览调用的详细位置:")
    for line_num, context, call_line in call_contexts:
        print(f"  行 {line_num}: {context}")
        print(f"    调用: {call_line}")
    
    print("\n5. 总结...")
    
    if all_methods_ok:
        print("🎉 所有检查通过！停止预览功能已正确实现。")
        print("✓ 所有关键操作都添加了停止预览调用")
        print("✓ 停止预览方法功能完整")
        print("✓ 代码实现符合要求")
    else:
        print("❌ 检查发现问题，需要进一步完善。")
    
    return all_methods_ok


def main():
    """主函数"""
    try:
        success = check_stop_preview_calls()
        
        if success:
            print("\n" + "=" * 60)
            print("功能特点总结:")
            print("✓ 切换文件夹时自动停止预览")
            print("✓ 开始新搜索时自动停止预览") 
            print("✓ 删除文件时自动停止预览")
            print("✓ 重命名文件时自动停止预览")
            print("✓ 移除文件时自动停止预览")
            print("✓ 加载结果时自动停止预览")
            print("✓ 目录树切换时自动停止预览")
            print("✓ 完整的预览状态管理")
            print("=" * 60)
        
    except Exception as e:
        print(f"验证过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
