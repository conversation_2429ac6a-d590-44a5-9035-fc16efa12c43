#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重复文件查找器的缓存优化功能
"""

import os
import sys
import time
import tempfile
import shutil
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_files():
    """创建测试文件"""
    test_dir = tempfile.mkdtemp(prefix="cache_test_")
    print(f"创建测试目录: {test_dir}")
    
    # 创建一些相同大小的文件
    test_files = []
    
    # 创建3个1KB的文件
    for i in range(3):
        file_path = os.path.join(test_dir, f"test_1kb_{i}.txt")
        with open(file_path, 'w') as f:
            f.write('x' * 1024)  # 1KB
        test_files.append(file_path)
    
    # 创建2个2KB的文件
    for i in range(2):
        file_path = os.path.join(test_dir, f"test_2kb_{i}.txt")
        with open(file_path, 'w') as f:
            f.write('y' * 2048)  # 2KB
        test_files.append(file_path)
    
    # 创建1个独特大小的文件
    file_path = os.path.join(test_dir, "unique.txt")
    with open(file_path, 'w') as f:
        f.write('z' * 512)  # 512B
    test_files.append(file_path)
    
    print(f"创建了 {len(test_files)} 个测试文件")
    return test_dir, test_files

def test_cache_performance():
    """测试缓存性能"""
    print("=" * 50)
    print("开始测试缓存性能")
    print("=" * 50)
    
    # 创建测试文件
    test_dir, test_files = create_test_files()
    
    try:
        # 导入重复文件查找器
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        # 创建应用实例
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        app = FileSearchApp(root)
        
        # 设置测试目录
        app.current_directory = test_dir
        
        print(f"\n1. 测试文件大小缓存功能")
        print("-" * 30)
        
        # 第一次获取文件大小（应该会缓存）
        start_time = time.time()
        for file_path in test_files:
            size = app.get_file_size_cached(file_path)
            print(f"文件: {os.path.basename(file_path)}, 大小: {size} 字节")
        first_time = time.time() - start_time
        print(f"第一次获取耗时: {first_time:.4f} 秒")
        
        # 第二次获取文件大小（应该从缓存读取）
        start_time = time.time()
        for file_path in test_files:
            size = app.get_file_size_cached(file_path)
        second_time = time.time() - start_time
        print(f"第二次获取耗时: {second_time:.4f} 秒")
        
        if second_time < first_time:
            print(f"✓ 缓存生效！性能提升: {((first_time - second_time) / first_time * 100):.1f}%")
        else:
            print("✗ 缓存可能未生效")
        
        print(f"\n2. 测试缓存清理功能")
        print("-" * 30)
        
        # 显示缓存状态
        print(f"当前文件信息缓存数量: {len(app._file_info_cache)}")
        print(f"当前预览缓存数量: {len(app._preview_cache)}")
        
        # 删除一个文件
        deleted_file = test_files[0]
        os.remove(deleted_file)
        print(f"删除文件: {os.path.basename(deleted_file)}")
        
        # 执行智能缓存清理
        app.smart_cache_cleanup()
        print(f"清理后文件信息缓存数量: {len(app._file_info_cache)}")
        
        print(f"\n3. 测试删除后缓存恢复功能")
        print("-" * 30)
        
        # 模拟搜索结果
        app.current_results = {
            1024: [test_files[1], test_files[2]],  # 剩余的1KB文件
            2048: test_files[3:5]  # 2KB文件
        }
        app._scan_results_cache = app.current_results.copy()
        app._last_scan_directory = test_dir
        app._last_scan_timestamp = time.time()
        
        print("模拟删除文件后的缓存恢复...")
        success = app.load_from_cache_after_delete([deleted_file])
        
        if success:
            print("✓ 缓存恢复成功")
            print(f"当前结果组数: {len(app.current_results)}")
        else:
            print("✗ 缓存恢复失败")
        
        print(f"\n4. 测试批量删除的缓存优化")
        print("-" * 30)
        
        # 模拟批量删除
        deleted_files = test_files[1:3]  # 删除2个文件
        print(f"模拟删除 {len(deleted_files)} 个文件")
        
        start_time = time.time()
        success = app.load_from_cache_after_delete(deleted_files)
        cache_time = time.time() - start_time
        
        print(f"缓存恢复耗时: {cache_time:.4f} 秒")
        
        if success:
            print("✓ 批量删除缓存优化成功")
        else:
            print("✗ 批量删除缓存优化失败")
        
        root.destroy()
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"\n清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理测试目录失败: {str(e)}")

def test_memory_usage():
    """测试内存使用情况"""
    print("\n" + "=" * 50)
    print("测试内存使用情况")
    print("=" * 50)
    
    try:
        import psutil
        process = psutil.Process()
        
        print(f"当前内存使用: {process.memory_info().rss / 1024 / 1024:.2f} MB")
        
        # 创建大量缓存数据
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 模拟大量文件信息缓存
        for i in range(1000):
            fake_path = f"/fake/path/file_{i}.txt"
            app.cache_file_info(fake_path, {
                'size': i * 1024,
                'duration': i * 10.5,
                'last_modified': time.time()
            })
        
        print(f"缓存1000个文件信息后内存使用: {process.memory_info().rss / 1024 / 1024:.2f} MB")
        print(f"文件信息缓存数量: {len(app._file_info_cache)}")
        
        # 清理缓存
        app._file_info_cache.clear()
        print(f"清理缓存后内存使用: {process.memory_info().rss / 1024 / 1024:.2f} MB")
        
        root.destroy()
        
    except ImportError:
        print("需要安装 psutil 来测试内存使用: pip install psutil")
    except Exception as e:
        print(f"内存测试出错: {str(e)}")

if __name__ == "__main__":
    print("重复文件查找器缓存优化测试")
    print("=" * 50)
    
    test_cache_performance()
    test_memory_usage()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
    
    print("\n缓存优化功能说明:")
    print("1. 文件信息缓存: 避免重复获取文件大小和时长")
    print("2. 智能缓存清理: 自动清理不存在文件的缓存")
    print("3. 删除后缓存恢复: 删除文件后从缓存快速恢复界面")
    print("4. 批量操作优化: 批量删除时使用缓存而非重新扫描")
    print("5. 预览缓存管理: 限制预览缓存数量，避免内存泄漏")
