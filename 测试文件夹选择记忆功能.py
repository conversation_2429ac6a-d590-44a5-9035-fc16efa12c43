"""
测试文件夹选择记忆功能
"""
import os
import tempfile
import shutil
import tkinter as tk
import json
import time


def create_test_directories():
    """创建测试目录"""
    base_dir = tempfile.mkdtemp(prefix="folder_memory_test_")
    print(f"创建测试基础目录: {base_dir}")
    
    # 创建几个子目录
    test_dirs = ["测试目录1", "测试目录2", "深层目录/子目录"]
    created_dirs = []
    
    for dir_name in test_dirs:
        dir_path = os.path.join(base_dir, dir_name)
        os.makedirs(dir_path, exist_ok=True)
        created_dirs.append(dir_path)
        print(f"创建目录: {dir_path}")
    
    return base_dir, created_dirs


def test_folder_selection_memory():
    """测试文件夹选择记忆功能"""
    print("=" * 60)
    print("文件夹选择记忆功能测试")
    print("=" * 60)
    
    # 创建测试目录
    test_base_dir, test_dirs = create_test_directories()
    
    try:
        # 导入主程序
        import sys
        sys.path.append('.')
        
        from 重复文件查找器 import FileSearchApp
        
        print("1. 第一次运行程序，测试配置加载...")
        
        # 第一次运行
        root1 = tk.Tk()
        root1.title("文件夹记忆测试 - 第一次运行")
        root1.geometry("1000x600")
        
        app1 = FileSearchApp(root1)
        
        # 检查配置文件路径
        config_file = app1.config_file
        print(f"配置文件路径: {config_file}")
        
        # 如果存在旧的配置文件，先删除
        if os.path.exists(config_file):
            os.remove(config_file)
            print("已删除旧的配置文件")
        
        def first_run_test():
            try:
                print("2. 检查初始状态...")
                
                # 检查初始状态
                initial_current_dir = app1.current_directory
                initial_last_dir = app1.last_directory
                
                print(f"初始当前目录: {initial_current_dir}")
                print(f"初始上次目录: {initial_last_dir}")
                
                if not initial_current_dir and not initial_last_dir:
                    print("✓ 初始状态正确（没有保存的目录）")
                else:
                    print("❌ 初始状态异常")
                    return False
                
                print("3. 模拟选择文件夹...")
                
                # 模拟选择文件夹
                test_dir = test_dirs[0]  # 选择第一个测试目录
                print(f"模拟选择目录: {test_dir}")
                
                # 直接设置目录（模拟用户选择）
                app1.current_directory = test_dir
                app1.path_var.set(f"当前文件夹: {test_dir}")
                app1.search_by_size_btn.configure(state='normal')
                app1.search_by_duration_btn.configure(state='normal')
                
                # 保存配置
                app1.save_config()
                root1.update()
                
                print("✓ 已选择目录并保存配置")
                
                # 检查配置文件是否创建
                if os.path.exists(config_file):
                    print("✓ 配置文件已创建")
                    
                    # 读取配置文件内容
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    
                    saved_last_directory = config_data.get("last_directory", "")
                    print(f"配置文件中保存的目录: {saved_last_directory}")
                    
                    if saved_last_directory == test_dir:
                        print("✓ 目录保存正确")
                        return True
                    else:
                        print(f"❌ 目录保存错误: {saved_last_directory} != {test_dir}")
                        return False
                else:
                    print("❌ 配置文件未创建")
                    return False
                    
            except Exception as e:
                print(f"❌ 第一次运行测试失败: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        # 执行第一次测试
        root1.after(500, lambda: test_and_close(first_run_test, root1, app1))
        root1.mainloop()
        
        # 等待一下确保文件保存完成
        time.sleep(0.5)
        
        print("\n4. 第二次运行程序，测试记忆恢复...")
        
        # 第二次运行
        root2 = tk.Tk()
        root2.title("文件夹记忆测试 - 第二次运行")
        root2.geometry("1000x600")
        
        app2 = FileSearchApp(root2)
        
        def second_run_test():
            try:
                # 等待初始化完成
                time.sleep(0.5)
                root2.update()
                
                print("5. 检查是否恢复了上次选择的目录...")
                
                # 检查是否恢复了目录
                restored_current_dir = app2.current_directory
                restored_last_dir = app2.last_directory
                path_display = app2.path_var.get()
                
                print(f"恢复的当前目录: {restored_current_dir}")
                print(f"恢复的上次目录: {restored_last_dir}")
                print(f"路径显示: {path_display}")
                
                # 检查目录是否正确恢复
                expected_dir = test_dirs[0]
                if restored_current_dir == expected_dir:
                    print("✓ 当前目录恢复正确")
                else:
                    print(f"❌ 当前目录恢复错误: {restored_current_dir} != {expected_dir}")
                    return False
                
                if restored_last_dir == expected_dir:
                    print("✓ 上次目录记录正确")
                else:
                    print(f"❌ 上次目录记录错误: {restored_last_dir} != {expected_dir}")
                    return False
                
                if expected_dir in path_display:
                    print("✓ 路径显示正确")
                else:
                    print(f"❌ 路径显示错误: {path_display}")
                    return False
                
                # 检查搜索按钮状态
                search_btn_state = app2.search_by_size_btn.cget('state')
                if search_btn_state == 'normal':
                    print("✓ 搜索按钮已启用")
                else:
                    print(f"❌ 搜索按钮状态错误: {search_btn_state}")
                    return False
                
                print("6. 测试文件夹选择对话框的初始目录...")
                
                # 检查select_directory方法中的初始目录逻辑
                # 这里我们不能直接测试文件对话框，但可以检查相关属性
                if app2.last_directory and os.path.exists(app2.last_directory):
                    print("✓ 文件对话框将使用正确的初始目录")
                else:
                    print("❌ 文件对话框初始目录可能有问题")
                    return False
                
                print("7. 测试更换目录后的记忆更新...")
                
                # 模拟选择另一个目录
                new_test_dir = test_dirs[1]
                print(f"模拟选择新目录: {new_test_dir}")
                
                app2.current_directory = new_test_dir
                app2.path_var.set(f"当前文件夹: {new_test_dir}")
                app2.save_config()
                root2.update()
                
                # 检查配置是否更新
                with open(config_file, 'r', encoding='utf-8') as f:
                    updated_config = json.load(f)
                
                updated_last_directory = updated_config.get("last_directory", "")
                if updated_last_directory == new_test_dir:
                    print("✓ 配置更新正确")
                else:
                    print(f"❌ 配置更新错误: {updated_last_directory} != {new_test_dir}")
                    return False
                
                print("8. 所有测试通过！")
                return True
                
            except Exception as e:
                print(f"❌ 第二次运行测试失败: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        # 执行第二次测试
        root2.after(1000, lambda: test_and_close(second_run_test, root2, app2))
        root2.mainloop()
        
        print("\n9. 测试无效目录的处理...")
        
        # 修改配置文件，设置一个不存在的目录
        invalid_dir = os.path.join(test_base_dir, "不存在的目录")
        invalid_config = {
            "last_directory": invalid_dir,
            "last_loaded_file": ""
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(invalid_config, f, ensure_ascii=False, indent=2)
        
        print(f"设置无效目录: {invalid_dir}")
        
        # 第三次运行，测试无效目录处理
        root3 = tk.Tk()
        root3.title("文件夹记忆测试 - 无效目录测试")
        root3.geometry("1000x600")
        
        app3 = FileSearchApp(root3)
        
        def third_run_test():
            try:
                time.sleep(0.5)
                root3.update()
                
                # 检查是否正确处理了无效目录
                current_dir = app3.current_directory
                search_btn_state = app3.search_by_size_btn.cget('state')
                
                print(f"无效目录测试 - 当前目录: {current_dir}")
                print(f"无效目录测试 - 搜索按钮状态: {search_btn_state}")
                
                if not current_dir and search_btn_state == 'disabled':
                    print("✓ 无效目录处理正确")
                    return True
                else:
                    print("❌ 无效目录处理有问题")
                    return False
                    
            except Exception as e:
                print(f"❌ 无效目录测试失败: {e}")
                return False
        
        root3.after(1000, lambda: test_and_close(third_run_test, root3, app3))
        root3.mainloop()
        
        print("\n🎉 文件夹选择记忆功能测试完成！")
        
    finally:
        # 清理测试目录和配置文件
        try:
            shutil.rmtree(test_base_dir)
            print(f"清理测试目录: {test_base_dir}")
        except Exception as e:
            print(f"清理测试目录失败: {e}")
        
        # 清理配置文件
        try:
            if 'app1' in locals():
                config_file = app1.config_file
                if os.path.exists(config_file):
                    os.remove(config_file)
                    print(f"清理配置文件: {config_file}")
        except Exception as e:
            print(f"清理配置文件失败: {e}")


def test_and_close(test_func, root, app):
    """执行测试并关闭窗口"""
    try:
        success = test_func()
        if success:
            print("✓ 当前阶段测试通过")
        else:
            print("❌ 当前阶段测试失败")
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
    finally:
        # 保存配置并关闭
        try:
            app.save_config()
        except:
            pass
        root.quit()
        root.destroy()


def main():
    """主函数"""
    test_folder_selection_memory()


if __name__ == "__main__":
    main()
