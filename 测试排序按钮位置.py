"""
测试排序按钮移动到列表外的效果
"""
import os
import tempfile
import shutil
import tkinter as tk
from tkinter import messagebox
import time


def create_test_files():
    """创建测试文件"""
    test_dir = tempfile.mkdtemp(prefix="sort_button_test_")
    print(f"创建测试目录: {test_dir}")
    
    # 创建不同大小的文件组，确保有足够的内容可以滚动
    file_groups = [
        (1024, 8),      # 1KB文件，8个
        (2048, 6),      # 2KB文件，6个  
        (4096, 5),      # 4KB文件，5个
        (8192, 7),      # 8KB文件，7个
        (16384, 4),     # 16KB文件，4个
        (32768, 3),     # 32KB文件，3个
        (65536, 5),     # 64KB文件，5个
        (131072, 2),    # 128KB文件，2个
    ]
    
    created_files = []
    
    for size, count in file_groups:
        for i in range(count):
            filename = f"test_{size}_{i:02d}.dat"
            filepath = os.path.join(test_dir, filename)
            
            with open(filepath, 'wb') as f:
                f.write(b'0' * size)
            
            created_files.append(filepath)
    
    print(f"创建了 {len(created_files)} 个测试文件")
    return test_dir, created_files


def test_sort_button_visibility():
    """测试排序按钮的可见性和位置"""
    print("开始测试排序按钮位置...")
    
    # 创建测试文件
    test_dir, test_files = create_test_files()
    
    try:
        # 导入主程序
        import sys
        sys.path.append('.')
        
        # 创建主窗口
        root = tk.Tk()
        root.title("排序按钮位置测试")
        
        # 导入并创建应用实例
        from 重复文件查找器 import FileSearchApp
        app = FileSearchApp(root)
        
        # 设置测试目录
        app.current_directory = test_dir
        app.path_var.set(f"当前目录: {test_dir}")
        app.search_by_size_btn.configure(state='normal')
        app.search_by_duration_btn.configure(state='normal')
        
        print("1. 检查初始状态...")
        
        # 检查排序按钮初始状态（应该隐藏）
        if app.sort_button_frame.winfo_viewable():
            print("❌ 排序按钮框架在初始状态下应该隐藏")
            return False
        else:
            print("✓ 排序按钮框架初始状态正确（隐藏）")
        
        print("2. 执行搜索...")
        
        # 模拟搜索过程
        def simulate_search_and_test():
            try:
                # 执行搜索
                app._search_by_size_worker()
                
                print("3. 检查搜索后状态...")
                
                # 检查是否有结果
                if not app.current_results:
                    print("❌ 搜索没有找到重复文件")
                    return False
                
                print(f"✓ 找到 {len(app.current_results)} 组重复文件")
                
                # 检查排序按钮是否显示
                if not app.sort_button_frame.winfo_viewable():
                    print("❌ 搜索后排序按钮框架应该显示")
                    return False
                else:
                    print("✓ 搜索后排序按钮框架正确显示")
                
                # 检查排序按钮是否存在
                if not app.size_sort_btn.winfo_exists():
                    print("❌ 按大小排序按钮不存在")
                    return False
                else:
                    print("✓ 按大小排序按钮存在")
                
                if not app.duration_sort_btn.winfo_exists():
                    print("❌ 按时长排序按钮不存在")
                    return False
                else:
                    print("✓ 按时长排序按钮存在")
                
                # 检查排序状态标签
                if not app.sort_status_label.winfo_exists():
                    print("❌ 排序状态标签不存在")
                    return False
                else:
                    print("✓ 排序状态标签存在")
                
                print("4. 测试排序按钮功能...")
                
                # 测试按时长排序
                print("点击按时长排序按钮...")
                app.sort_by_duration_clicked()
                
                # 等待界面更新
                root.update()
                
                # 检查排序状态
                status_text = app.sort_status_label.cget("text")
                if "按时长排序" not in status_text:
                    print(f"❌ 排序状态显示错误: {status_text}")
                    return False
                else:
                    print(f"✓ 排序状态显示正确: {status_text}")
                
                print("5. 测试滚动时按钮可见性...")
                
                # 滚动到底部
                app.text_area.yview_moveto(1.0)
                root.update()
                
                # 检查排序按钮是否仍然可见
                if not app.sort_button_frame.winfo_viewable():
                    print("❌ 滚动后排序按钮应该仍然可见")
                    return False
                else:
                    print("✓ 滚动后排序按钮仍然可见")
                
                # 滚动到顶部
                app.text_area.yview_moveto(0.0)
                root.update()
                
                # 再次检查按钮可见性
                if not app.sort_button_frame.winfo_viewable():
                    print("❌ 滚动到顶部后排序按钮应该仍然可见")
                    return False
                else:
                    print("✓ 滚动到顶部后排序按钮仍然可见")
                
                print("6. 测试切换排序...")
                
                # 切换到按大小排序
                print("点击按大小排序按钮...")
                app.sort_by_size_clicked()
                
                # 等待界面更新
                root.update()
                
                # 检查排序状态
                status_text = app.sort_status_label.cget("text")
                if "按大小排序" not in status_text:
                    print(f"❌ 切换后排序状态显示错误: {status_text}")
                    return False
                else:
                    print(f"✓ 切换后排序状态显示正确: {status_text}")
                
                print("7. 测试删除文件后按钮状态...")
                
                # 删除一个文件测试
                first_group = list(app.current_results.values())[0]
                if len(first_group) > 1:
                    test_file = first_group[0]
                    
                    try:
                        os.remove(test_file)
                        
                        # 更新内存中的数据
                        for size, files in app.current_results.items():
                            if test_file in files:
                                files.remove(test_file)
                                if len(files) < 2:
                                    del app.current_results[size]
                                break
                        
                        # 重新显示结果
                        app.display_results(app.current_results, preserve_scroll=True)
                        root.update()
                        
                        # 检查按钮是否仍然可见
                        if app.current_results and not app.sort_button_frame.winfo_viewable():
                            print("❌ 删除文件后排序按钮应该仍然可见")
                            return False
                        elif app.current_results:
                            print("✓ 删除文件后排序按钮仍然可见")
                        
                    except Exception as e:
                        print(f"删除文件测试失败: {e}")
                
                print("8. 所有测试通过！")
                return True
                
            except Exception as e:
                print(f"❌ 测试过程中出错: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        # 在主线程中执行测试
        root.after(100, lambda: test_and_exit(simulate_search_and_test, root))
        
        # 运行主循环
        root.mainloop()
        
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理测试目录失败: {e}")


def test_and_exit(test_func, root):
    """执行测试并退出"""
    try:
        success = test_func()
        if success:
            print("\n🎉 所有测试通过！排序按钮位置修改成功。")
            print("✓ 排序按钮现在位于列表外部，始终可见")
            print("✓ 滚动列表时排序按钮不会消失")
            print("✓ 排序状态实时显示")
        else:
            print("\n❌ 测试失败！需要进一步检查修改。")
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
    finally:
        # 延迟关闭，让用户看到结果
        root.after(3000, root.quit)


def main():
    """主函数"""
    print("=" * 50)
    print("排序按钮位置测试")
    print("=" * 50)
    
    test_sort_button_visibility()


if __name__ == "__main__":
    main()
