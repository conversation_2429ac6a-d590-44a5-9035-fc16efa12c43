# 目录树功能实现说明

## 功能概述

在重复文件查找器的左侧添加了一个目录树组件，让用户可以方便地浏览和切换目录，无需每次都通过文件对话框选择目录。

## 界面布局变化

### 修改前的布局
```
┌─────────────────────────────────────────────────────────┐
│ 按钮区域                                                │
├─────────────────────────────────────────────────────────┤
│ 搜索结果                    │ 预览                      │
│                            │                          │
│                            │                          │
│                            │                          │
└─────────────────────────────────────────────────────────┘
│ 已选择文件                                              │
└─────────────────────────────────────────────────────────┘
```

### 修改后的布局
```
┌─────────────────────────────────────────────────────────┐
│ 按钮区域                                                │
├─────────────────────────────────────────────────────────┤
│ 目录浏览    │ 搜索结果              │ 预览              │
│ [刷新][展开] │                      │                  │
│ [折叠]      │                      │                  │
│ ├─C:        │                      │                  │
│ ├─D:        │                      │                  │
│ └─E:        │                      │                  │
│   ├─文档    │                      │                  │
│   ├─图片    │                      │                  │
│   └─视频    │                      │                  │
└─────────────────────────────────────────────────────────┘
│ 已选择文件                                              │
└─────────────────────────────────────────────────────────┘
```

## 主要功能特性

### 1. 目录树显示
- **多平台支持**：Windows显示所有驱动器，Linux/macOS从根目录开始
- **懒加载**：只在需要时加载子目录，提高性能
- **层级显示**：清晰的树形结构显示目录层级关系

### 2. 目录导航
- **单击选择**：单击目录项进行选择和预加载子目录
- **双击切换**：双击目录项直接切换为当前工作目录
- **自动展开**：切换目录时自动展开到目标路径

### 3. 交互功能
- **右键菜单**：提供设为当前目录、在文件管理器中打开等功能
- **控制按钮**：刷新、展开所有、折叠所有等快捷操作
- **滚动支持**：支持垂直滚动浏览大量目录

## 技术实现细节

### 1. 界面组件创建

```python
def create_directory_tree(self):
    """创建目录树组件"""
    # 创建目录树控制按钮框架
    self.dir_control_frame = ttk.Frame(self.directory_frame)
    
    # 添加控制按钮
    self.refresh_tree_btn = ttk.Button(self.dir_control_frame, text="刷新")
    self.expand_all_btn = ttk.Button(self.dir_control_frame, text="展开")
    self.collapse_all_btn = ttk.Button(self.dir_control_frame, text="折叠")
    
    # 创建目录树
    self.directory_tree = ttk.Treeview(self.dir_tree_container, selectmode='browse')
    
    # 绑定事件
    self.directory_tree.bind("<<TreeviewSelect>>", self.on_directory_select)
    self.directory_tree.bind("<Double-1>", self.on_directory_double_click)
    self.directory_tree.bind("<Button-3>", self.on_directory_right_click)
```

### 2. 目录树初始化

```python
def initialize_directory_tree(self):
    """初始化目录树"""
    if platform.system() == 'Windows':
        # Windows系统：添加所有驱动器
        for drive in string.ascii_uppercase:
            drive_path = f"{drive}:\\"
            if os.path.exists(drive_path):
                drive_item = self.directory_tree.insert("", "end", 
                                                      text=f"{drive}:", 
                                                      tags=(drive_path,))
                # 添加虚拟子项以显示展开图标
                self.directory_tree.insert(drive_item, "end", text="加载中...")
    else:
        # Unix/Linux系统：从根目录开始
        root_item = self.directory_tree.insert("", "end", text="/", tags=("/",))
        self.directory_tree.insert(root_item, "end", text="加载中...")
```

### 3. 懒加载机制

```python
def load_directory_children(self, parent_item):
    """加载目录的子项"""
    # 获取父目录路径
    parent_tags = self.directory_tree.item(parent_item, "tags")
    parent_path = parent_tags[0]
    
    # 清除虚拟子项
    for child in self.directory_tree.get_children(parent_item):
        if self.directory_tree.item(child, "text") == "加载中...":
            self.directory_tree.delete(child)
    
    # 加载实际子目录
    for item in os.listdir(parent_path):
        item_path = os.path.join(parent_path, item)
        if os.path.isdir(item_path):
            child_item = self.directory_tree.insert(parent_item, "end",
                                                   text=item, tags=(item_path,))
            # 如果有子目录，添加虚拟子项
            if self.has_subdirectories(item_path):
                self.directory_tree.insert(child_item, "end", text="加载中...")
```

### 4. 事件处理

#### 目录选择事件
```python
def on_directory_select(self, event):
    """目录树选择事件"""
    selection = self.directory_tree.selection()
    if selection:
        item = selection[0]
        # 预加载子目录
        self.load_directory_children(item)
```

#### 双击切换目录
```python
def on_directory_double_click(self, event):
    """目录树双击事件"""
    selection = self.directory_tree.selection()
    if selection:
        item = selection[0]
        tags = self.directory_tree.item(item, "tags")
        if tags:
            directory_path = tags[0]
            # 设置为当前目录
            self.current_directory = directory_path
            # 更新界面状态
            self.update_ui_for_directory_change(directory_path)
```

#### 右键菜单
```python
def on_directory_right_click(self, event):
    """目录树右键菜单"""
    item = self.directory_tree.identify_row(event.y)
    if item:
        # 创建右键菜单
        context_menu = tk.Menu(self.root, tearoff=0)
        context_menu.add_command(label="设为当前目录", command=...)
        context_menu.add_command(label="在文件管理器中打开", command=...)
        context_menu.add_command(label="刷新", command=...)
        context_menu.post(event.x_root, event.y_root)
```

### 5. 路径展开功能

```python
def expand_to_directory(self, target_path):
    """展开目录树到指定路径"""
    # 分解路径
    path_parts = self.split_path(target_path)
    
    # 逐级展开
    current_item = ""
    for part in path_parts:
        # 查找对应的树项
        found_item = self.find_tree_item(current_item, part)
        if found_item:
            current_item = found_item
            # 展开该项
            self.load_directory_children(current_item)
            self.directory_tree.item(current_item, open=True)
    
    # 选中目标目录
    if current_item:
        self.directory_tree.selection_set(current_item)
        self.directory_tree.see(current_item)
```

## 用户交互流程

### 1. 浏览目录
1. 用户在左侧目录树中看到系统的驱动器或根目录
2. 点击展开图标或选择目录项来浏览子目录
3. 目录树会懒加载显示子目录内容

### 2. 切换工作目录
1. 用户双击目标目录
2. 程序自动设置该目录为当前工作目录
3. 更新路径显示和界面状态
4. 启用搜索按钮
5. 清空之前的搜索结果

### 3. 快捷操作
1. 右键点击目录显示上下文菜单
2. 选择"设为当前目录"或"在文件管理器中打开"
3. 使用控制按钮进行刷新、展开、折叠操作

## 性能优化

### 1. 懒加载策略
- 只在用户展开目录时才加载子目录
- 避免一次性加载整个文件系统树
- 减少内存占用和初始化时间

### 2. 缓存机制
- 已加载的目录结构会保留在树中
- 避免重复加载相同目录
- 提供刷新功能更新目录状态

### 3. 异常处理
- 处理权限不足的目录访问
- 跳过无法访问的系统目录
- 提供友好的错误提示

## 兼容性支持

### 1. 跨平台支持
- **Windows**：显示所有可用驱动器（C:, D:, E: 等）
- **macOS/Linux**：从根目录 "/" 开始显示
- 自动检测操作系统并适配显示方式

### 2. 路径处理
- 使用 `os.path` 模块处理路径分隔符
- 支持长路径和特殊字符
- 处理网络驱动器和符号链接

## 使用建议

### 1. 日常使用
- 使用目录树快速浏览和切换目录
- 双击目录直接设为工作目录
- 利用右键菜单进行快捷操作

### 2. 大型目录结构
- 使用折叠功能管理复杂的目录树
- 利用刷新功能更新目录状态
- 避免展开过多目录以保持性能

### 3. 配合搜索功能
- 先通过目录树定位到目标目录
- 再使用搜索功能查找重复文件
- 结合预览功能确认文件内容

## 总结

目录树功能的添加大大提升了重复文件查找器的易用性：

1. **便捷导航**：无需反复打开文件对话框
2. **直观显示**：清晰的树形结构显示目录关系
3. **快速切换**：双击即可切换工作目录
4. **丰富交互**：支持右键菜单和控制按钮
5. **性能优化**：懒加载机制保证流畅体验

这个功能让用户能够更高效地管理和浏览文件系统，特别适合需要在多个目录间频繁切换的使用场景。
