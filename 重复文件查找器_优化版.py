"""
重复文件查找器 - 优化版本
使用模块化设计，提高性能和可维护性
"""
import os
import sys
import tkinter as tk
from tkinter import filedialog, messagebox
import threading
import platform
import ctypes
from ctypes import windll

# 导入自定义模块
from file_scanner import FileScanner
from file_preview import FilePreviewManager
from ui_manager import UIManager
from config_manager import ConfigManager
from async_task_manager import AsyncTaskManager, ProgressTracker
from memory_manager import MemoryManager


class OptimizedFileSearchApp:
    """优化版重复文件查找器主应用"""
    
    def __init__(self, root):
        self.root = root

        # 初始化各个管理器
        self.config_manager = ConfigManager()
        self.ui_manager = UIManager(root)
        self.file_scanner = FileScanner()
        self.task_manager = AsyncTaskManager(max_workers=self.config_manager.get('scan_thread_count', 4))
        self.memory_manager = MemoryManager(max_memory_mb=self.config_manager.get('max_memory_mb', 500))

        # 创建主界面
        self.ui_components = self.ui_manager.create_main_ui()

        # 初始化预览管理器
        self.preview_manager = FilePreviewManager(
            self.ui_components['preview_label'],
            self.ui_components['preview_filename_var'],
            self.ui_components['buttons']['stop_preview']
        )

        # 应用状态
        self.current_directory = ""
        self.current_results = {}
        self.current_search_type = "size"

        # 设置内存管理回调
        self.memory_manager.register_cleanup_callback(self._on_memory_cleanup)
        self.memory_manager.register_memory_warning_callback(self._on_memory_warning)

        # 绑定事件
        self._bind_events()

        # 应用配置
        self._apply_config()

        # Windows系统特殊处理
        self._setup_windows_specific()

        print("优化版重复文件查找器已启动")
    
    def _bind_events(self):
        """绑定UI事件"""
        buttons = self.ui_components['buttons']
        
        # 绑定按钮事件
        buttons['select'].configure(command=self.select_directory)
        buttons['search_size'].configure(command=self.search_by_size)
        buttons['search_duration'].configure(command=self.search_by_duration)
        buttons['save'].configure(command=self.save_results)
        buttons['load'].configure(command=self.load_results)
        buttons['select_all'].configure(command=self.select_all_files)
        buttons['deselect_all'].configure(command=self.deselect_all_files)
        buttons['delete_selected'].configure(command=self.delete_selected_files)
        buttons['remove_from_list'].configure(command=self.remove_selected_from_list)
        buttons['stop_preview'].configure(command=self.preview_manager.stop_preview)
        
        # 绑定文本区域事件
        text_area = self.ui_components['text_area']
        text_area.tag_configure("file", foreground="blue", underline=1)
        text_area.tag_bind("file", "<Button-1>", self.on_file_click)
        text_area.tag_bind("file", "<Enter>", lambda e: text_area.configure(cursor="hand2"))
        text_area.tag_bind("file", "<Leave>", lambda e: text_area.configure(cursor=""))
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def _apply_config(self):
        """应用配置"""
        # 应用窗口几何设置
        geometry = self.config_manager.get('window_geometry', '1400x768')
        self.root.geometry(geometry)
        
        # 应用其他UI设置
        include_images = self.config_manager.get('include_images', False)
        self.ui_components['include_images_var'].set(include_images)
        
        same_dir = self.config_manager.get('same_dir_selection', False)
        self.ui_components['same_dir_var'].set(same_dir)
        
        same_time = self.config_manager.get('same_time_selection', False)
        self.ui_components['same_time_var'].set(same_time)
        
        # 设置最后使用的目录
        last_dir = self.config_manager.get('last_directory', '')
        if last_dir and os.path.exists(last_dir):
            self.current_directory = last_dir
            self.ui_manager.update_path_display(last_dir)
            self.ui_manager.enable_search_buttons()
    
    def _setup_windows_specific(self):
        """Windows系统特殊设置"""
        if platform.system() == 'Windows':
            try:
                # 设置窗口样式
                GWL_EXSTYLE = -20
                WS_EX_APPWINDOW = 0x00040000
                WS_EX_TOOLWINDOW = 0x00000080
                hwnd = self.root.winfo_id()
                style = windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
                style = style & ~WS_EX_TOOLWINDOW
                style = style | WS_EX_APPWINDOW
                windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, style)
                
                # 将窗口设置为前台
                self.root.lift()
                self.root.attributes('-topmost', True)
                self.root.attributes('-topmost', False)
            except Exception as e:
                print(f"Windows特殊处理出错: {str(e)}")
    
    def select_directory(self):
        """选择目录"""
        initial_dir = self.current_directory or self.config_manager.get('last_directory', '')
        directory = filedialog.askdirectory(initialdir=initial_dir)
        
        if directory:
            self.current_directory = directory
            self.config_manager.set('last_directory', directory)
            self.config_manager.save_config()
            
            self.ui_manager.update_path_display(directory)
            self.ui_manager.enable_search_buttons()
            self.ui_manager.update_status(f"已选择目录: {directory}")
    
    def search_by_size(self):
        """按文件大小搜索重复文件"""
        if not self.current_directory:
            messagebox.showwarning("警告", "请先选择要搜索的目录")
            return
        
        self.current_search_type = "size"
        include_images = self.ui_components['include_images_var'].get()
        
        # 创建进度跟踪器
        progress_tracker = ProgressTracker(
            total=100,
            callback=self._update_progress
        )
        
        # 提交异步任务
        task_id = self.task_manager.submit_task(
            self.file_scanner.scan_files_by_size,
            self.current_directory,
            include_images,
            progress_callback=lambda p: progress_tracker.set_progress(int(p)),
            status_callback=self._update_status,
            callback=self._on_search_complete,
            error_callback=self._on_search_error,
            task_name="按大小搜索重复文件"
        )
        
        self.ui_manager.update_status("正在搜索相同大小的文件...")
        self.ui_manager.disable_search_buttons()
    
    def search_by_duration(self):
        """按视频时长搜索重复文件"""
        if not self.current_directory:
            messagebox.showwarning("警告", "请先选择要搜索的目录")
            return
        
        self.current_search_type = "duration"
        
        # 创建进度跟踪器
        progress_tracker = ProgressTracker(
            total=100,
            callback=self._update_progress
        )
        
        # 提交异步任务
        task_id = self.task_manager.submit_task(
            self.file_scanner.scan_files_by_duration,
            self.current_directory,
            progress_callback=lambda p: progress_tracker.set_progress(int(p)),
            status_callback=self._update_status,
            callback=self._on_search_complete,
            error_callback=self._on_search_error,
            task_name="按时长搜索重复文件"
        )
        
        self.ui_manager.update_status("正在搜索相同时长的视频文件...")
        self.ui_manager.disable_search_buttons()
    
    def _update_progress(self, progress_info):
        """更新进度显示"""
        self.ui_manager.update_progress(progress_info['percentage'])
    
    def _update_status(self, status):
        """更新状态显示"""
        self.ui_manager.update_current_file(status)
    
    def _on_search_complete(self, results):
        """搜索完成回调"""
        self.current_results = results
        
        # 在主线程中更新UI
        self.root.after(0, self._update_ui_after_search, results)
    
    def _on_search_error(self, error):
        """搜索错误回调"""
        error_msg = f"搜索失败: {str(error)}"
        self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
        self.root.after(0, self._reset_ui_after_search)
    
    def _update_ui_after_search(self, results):
        """搜索完成后更新UI"""
        if results:
            self.display_results(results)
            self.ui_manager.enable_save_button()
            self.ui_manager.update_status(f"搜索完成，找到 {len(results)} 组重复文件")
        else:
            self.ui_manager.clear_results()
            self.ui_components['text_area'].insert(tk.END, "未找到重复文件\n")
            self.ui_manager.update_status("搜索完成，未找到重复文件")
        
        self.ui_manager.enable_search_buttons()
        self.ui_manager.update_progress(0)
        self.ui_manager.update_current_file("")
    
    def _reset_ui_after_search(self):
        """搜索失败后重置UI"""
        self.ui_manager.enable_search_buttons()
        self.ui_manager.update_progress(0)
        self.ui_manager.update_current_file("")
        self.ui_manager.update_status("搜索失败")
    
    def display_results(self, results):
        """显示搜索结果"""
        text_area = self.ui_components['text_area']
        text_area.delete(1.0, tk.END)
        
        if not results:
            text_area.insert(tk.END, "未找到重复文件\n")
            return
        
        # 排序结果
        if self.current_search_type == "duration":
            sorted_results = sorted(results.items(), key=lambda x: x[0], reverse=True)
            text_area.insert(tk.END, f"按时长排序：从长到短，共 {len(results)} 组\n\n")
        else:
            sorted_results = sorted(results.items(), key=lambda x: x[0], reverse=True)
            text_area.insert(tk.END, f"按大小排序：从大到小，共 {len(results)} 组\n\n")
        
        # 显示每组结果
        for i, (key, files) in enumerate(sorted_results):
            if self.current_search_type == "duration":
                duration_str = self.ui_manager.format_duration(key)
                text_area.insert(tk.END, f"视频时长: {duration_str} ({len(files)} 个文件)\n")
            else:
                size_str = self.ui_manager.format_file_size(key)
                text_area.insert(tk.END, f"文件大小: {size_str} ({len(files)} 个文件)\n")
            
            text_area.insert(tk.END, "=" * 50 + "\n")
            
            # 显示文件列表
            for file_path in files:
                relative_path = self.ui_manager.get_relative_path(file_path, self.current_directory)
                text_area.insert(tk.END, f"  - {relative_path}\n")
                
                # 添加文件标签用于点击预览
                start_pos = text_area.index(f"end-1c linestart")
                end_pos = text_area.index(f"end-1c lineend")
                text_area.tag_add("file", start_pos, end_pos)
                text_area.tag_add(f"file_{file_path}", start_pos, end_pos)
            
            text_area.insert(tk.END, "\n")
    
    def on_file_click(self, event):
        """文件点击事件"""
        text_area = self.ui_components['text_area']
        index = text_area.index(f"@{event.x},{event.y}")
        tags = text_area.tag_names(index)
        
        if "file" in tags:
            # 提取文件路径
            line_text = text_area.get(f"{index} linestart", f"{index} lineend")
            if line_text.strip().startswith("  - "):
                relative_path = line_text.strip()[4:]  # 移除 "  - "
                file_path = os.path.join(self.current_directory, relative_path)
                
                # 异步预览文件
                self.task_manager.submit_task(
                    self.preview_manager.preview_file,
                    file_path,
                    self.root,
                    task_name=f"预览文件: {os.path.basename(file_path)}"
                )
    
    def save_results(self):
        """保存搜索结果"""
        if not self.current_results:
            messagebox.showwarning("警告", "没有可保存的结果")
            return
        
        filepath = self.config_manager.save_results_to_file(
            self.current_results,
            self.current_directory
        )
        
        if filepath:
            messagebox.showinfo("成功", f"结果已保存到:\n{filepath}")
            self.ui_manager.update_status(f"结果已保存: {os.path.basename(filepath)}")
        else:
            messagebox.showerror("错误", "保存结果失败")
    
    def load_results(self):
        """加载搜索结果"""
        # 获取最近的结果文件
        recent_files = self.config_manager.get_recent_results_files()
        
        if recent_files:
            # 默认选择最近的文件
            initial_file = recent_files[0]
            initial_dir = os.path.dirname(initial_file)
        else:
            initial_dir = os.path.join(os.path.expanduser("~"), "Documents", "DuplicateFiles")
            initial_file = ""
        
        filepath = filedialog.askopenfilename(
            title="选择结果文件",
            initialdir=initial_dir,
            initialfile=os.path.basename(initial_file) if initial_file else "",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if filepath:
            data = self.config_manager.load_results_from_file(filepath)
            if data:
                self.current_results = data.get('results', {})
                self.current_directory = data.get('directory', '')
                
                if self.current_directory:
                    self.ui_manager.update_path_display(self.current_directory)
                    self.ui_manager.enable_search_buttons()
                
                self.display_results(self.current_results)
                
                if self.current_results:
                    self.ui_manager.enable_save_button()
                
                timestamp = data.get('timestamp', '')
                self.ui_manager.update_status(f"已加载结果文件: {os.path.basename(filepath)} ({timestamp})")
            else:
                messagebox.showerror("错误", "加载结果文件失败")
    
    def select_all_files(self):
        """全选文件"""
        # TODO: 实现全选功能
        pass
    
    def deselect_all_files(self):
        """取消全选"""
        # TODO: 实现取消全选功能
        pass
    
    def delete_selected_files(self):
        """删除选中的文件"""
        # TODO: 实现删除功能
        pass
    
    def remove_selected_from_list(self):
        """从列表中移除选中的文件"""
        # TODO: 实现移除功能
        pass
    
    def _on_memory_cleanup(self):
        """内存清理回调"""
        try:
            # 清理预览缓存
            self.preview_manager.cleanup_invalid_cache()

            # 清理文件扫描器缓存
            self.file_scanner.cleanup_invalid_cache()

            print("内存清理回调执行完成")

        except Exception as e:
            print(f"内存清理回调失败: {e}")

    def _on_memory_warning(self, current_memory):
        """内存警告回调"""
        memory_mb = current_memory / 1024 / 1024
        warning_msg = f"内存使用过高: {memory_mb:.1f} MB\n建议关闭一些预览或重启程序"

        # 在主线程中显示警告
        self.root.after(0, lambda: messagebox.showwarning("内存警告", warning_msg))

    def on_closing(self):
        """程序关闭时的清理工作"""
        try:
            # 保存当前窗口几何设置
            geometry = self.root.geometry()
            self.config_manager.set('window_geometry', geometry)

            # 保存UI设置
            self.config_manager.set('include_images', self.ui_components['include_images_var'].get())
            self.config_manager.set('same_dir_selection', self.ui_components['same_dir_var'].get())
            self.config_manager.set('same_time_selection', self.ui_components['same_time_var'].get())

            # 保存配置
            self.config_manager.save_config()

            # 停止内存监控
            self.memory_manager.stop_memory_monitoring()

            # 停止所有任务
            self.task_manager.shutdown(wait=False)

            # 清理预览资源
            self.preview_manager.stop_preview()

            # 清理所有缓存
            self.memory_manager.clear_all_caches()

            print("程序正常退出")

        except Exception as e:
            print(f"程序退出时出错: {e}")

        finally:
            self.root.destroy()


def main():
    """主函数"""
    root = tk.Tk()
    app = OptimizedFileSearchApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()
