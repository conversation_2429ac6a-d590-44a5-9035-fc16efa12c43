#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试重复文件查找器的基本功能
"""

import os
import sys
import tempfile
import shutil
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_functionality():
    """测试基本功能"""
    print("测试重复文件查找器基本功能")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = tempfile.mkdtemp(prefix="basic_test_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 创建一些重复文件
        # 创建3个1KB的相同文件
        content_1kb = 'A' * 1024
        for i in range(3):
            file_path = os.path.join(test_dir, f"file_1kb_{i}.txt")
            with open(file_path, 'w') as f:
                f.write(content_1kb)
            print(f"创建文件: {os.path.basename(file_path)} (1KB)")
        
        # 创建2个2KB的相同文件
        content_2kb = 'B' * 2048
        for i in range(2):
            file_path = os.path.join(test_dir, f"file_2kb_{i}.txt")
            with open(file_path, 'w') as f:
                f.write(content_2kb)
            print(f"创建文件: {os.path.basename(file_path)} (2KB)")
        
        # 创建1个独特文件
        unique_file = os.path.join(test_dir, "unique.txt")
        with open(unique_file, 'w') as f:
            f.write('C' * 512)
        print(f"创建文件: unique.txt (512B)")
        
        # 导入并测试应用
        from 重复文件查找器 import FileSearchApp
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = FileSearchApp(root)
        
        # 设置目录
        app.current_directory = test_dir
        app.include_images_var.set(True)  # 包含所有文件类型
        
        print(f"\n开始搜索...")
        
        # 手动执行搜索逻辑
        from collections import defaultdict
        
        size_dict = defaultdict(list)
        video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.mpg', '.mpeg', '.ts', '.vob', '.asf', '.rm', '.rmvb'}
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.ico', '.svg', '.raw', '.cr2', '.nef', '.arw', '.dng'}
        
        allowed_extensions = video_extensions.copy()
        allowed_extensions.update(image_extensions)
        allowed_extensions.add('.txt')  # 添加txt文件
        
        print(f"允许的扩展名: {sorted(allowed_extensions)}")
        
        for root_dir, dirs, files in os.walk(test_dir):
            for file in files:
                file_path = os.path.join(root_dir, file)
                file_ext = os.path.splitext(file)[1].lower()
                
                print(f"检查文件: {file}, 扩展名: {file_ext}")
                
                if file_ext in allowed_extensions or not file_ext:  # 包含无扩展名文件
                    try:
                        file_size = os.path.getsize(file_path)
                        size_dict[file_size].append(file_path)
                        print(f"  -> 添加到大小组 {file_size}: {file}")
                    except Exception as e:
                        print(f"  -> 获取大小失败: {e}")
                else:
                    print(f"  -> 跳过（扩展名不匹配）")
        
        # 找出重复文件
        same_size_groups = {size: paths for size, paths in size_dict.items() if len(paths) > 1}
        
        print(f"\n搜索结果:")
        print(f"总文件数: {sum(len(files) for files in size_dict.values())}")
        print(f"重复文件组数: {len(same_size_groups)}")
        
        for size, files in same_size_groups.items():
            print(f"大小 {size} 字节: {len(files)} 个文件")
            for file_path in files:
                print(f"  - {os.path.basename(file_path)}")
        
        # 测试应用的搜索功能
        print(f"\n测试应用搜索功能...")
        app.current_results = same_size_groups
        
        if same_size_groups:
            print("✓ 找到重复文件，测试删除功能...")
            
            # 测试删除第一个文件
            first_size = next(iter(same_size_groups.keys()))
            first_file = same_size_groups[first_size][0]
            
            print(f"删除文件: {os.path.basename(first_file)}")
            
            start_time = time.time()
            app.delete_file(first_file, first_size)
            delete_time = time.time() - start_time
            
            print(f"删除耗时: {delete_time:.3f} 秒")
            print(f"剩余组数: {len(app.current_results)}")
            
            if delete_time < 0.1:
                print("✓ 删除响应速度优秀")
            else:
                print("⚠ 删除响应速度需要优化")
        else:
            print("✗ 未找到重复文件")
        
        root.destroy()
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"\n清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理失败: {str(e)}")

if __name__ == "__main__":
    test_basic_functionality()
