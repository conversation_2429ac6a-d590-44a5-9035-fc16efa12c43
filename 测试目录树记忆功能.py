"""
测试目录树记忆功能
"""
import os
import tempfile
import shutil
import tkinter as tk
import json
import time


def create_test_directory_structure():
    """创建测试目录结构"""
    base_dir = tempfile.mkdtemp(prefix="memory_test_")
    print(f"创建测试基础目录: {base_dir}")
    
    # 创建多层目录结构
    test_structure = {
        "项目A": {
            "源代码": ["main.py", "utils.py"],
            "文档": ["readme.txt", "changelog.txt"],
            "测试": ["test1.py", "test2.py"]
        },
        "项目B": {
            "前端": ["index.html", "style.css"],
            "后端": ["server.py", "database.py"]
        },
        "资源": {
            "图片": ["logo.png", "banner.jpg"],
            "音频": ["bgm.mp3", "sound.wav"]
        }
    }
    
    def create_structure(base_path, structure):
        for name, content in structure.items():
            dir_path = os.path.join(base_path, name)
            os.makedirs(dir_path, exist_ok=True)
            
            if isinstance(content, dict):
                create_structure(dir_path, content)
            elif isinstance(content, list):
                for filename in content:
                    file_path = os.path.join(dir_path, filename)
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(f"测试文件: {filename}")
    
    create_structure(base_dir, test_structure)
    return base_dir


def test_directory_tree_memory():
    """测试目录树记忆功能"""
    print("开始测试目录树记忆功能...")
    
    # 创建测试目录结构
    test_base_dir = create_test_directory_structure()
    
    try:
        # 导入主程序
        import sys
        sys.path.append('.')
        
        from 重复文件查找器 import FileSearchApp
        
        print("1. 第一次运行程序，展开一些目录...")
        
        # 第一次运行
        root1 = tk.Tk()
        root1.title("目录树记忆测试 - 第一次运行")
        root1.geometry("1000x600")
        
        app1 = FileSearchApp(root1)
        
        # 检查状态文件路径
        state_file = app1.directory_tree_state_file
        print(f"状态文件路径: {state_file}")
        
        # 如果存在旧的状态文件，先删除
        if os.path.exists(state_file):
            os.remove(state_file)
            print("已删除旧的状态文件")
        
        def first_run_test():
            try:
                # 展开到测试目录
                print(f"展开到测试目录: {test_base_dir}")
                app1.expand_to_directory(test_base_dir)
                root1.update()
                
                # 展开一些子目录
                test_subdirs = [
                    os.path.join(test_base_dir, "项目A"),
                    os.path.join(test_base_dir, "项目A", "源代码"),
                    os.path.join(test_base_dir, "资源")
                ]
                
                for subdir in test_subdirs:
                    if os.path.exists(subdir):
                        print(f"展开子目录: {subdir}")
                        app1.expand_to_directory_silent(subdir)
                        root1.update()
                
                # 更新展开状态
                app1.update_expanded_directories_state()
                
                # 检查展开状态记录
                expanded_count = len(app1.expanded_directories)
                print(f"记录了 {expanded_count} 个展开目录")
                
                if expanded_count > 0:
                    print("✓ 展开状态记录正常")
                    
                    # 保存状态
                    app1.save_directory_tree_state()
                    
                    # 检查状态文件是否创建
                    if os.path.exists(state_file):
                        print("✓ 状态文件创建成功")
                        
                        # 读取状态文件内容
                        with open(state_file, 'r', encoding='utf-8') as f:
                            state_data = json.load(f)
                        
                        saved_expanded = state_data.get("expanded_directories", [])
                        print(f"状态文件中保存了 {len(saved_expanded)} 个展开目录")
                        
                        if len(saved_expanded) > 0:
                            print("✓ 状态保存正常")
                            return True
                        else:
                            print("❌ 状态文件中没有保存展开目录")
                            return False
                    else:
                        print("❌ 状态文件未创建")
                        return False
                else:
                    print("❌ 没有记录展开状态")
                    return False
                    
            except Exception as e:
                print(f"❌ 第一次运行测试失败: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        # 执行第一次测试
        root1.after(500, lambda: test_and_close(first_run_test, root1, app1))
        root1.mainloop()
        
        # 等待一下确保文件保存完成
        time.sleep(0.5)
        
        print("\n2. 第二次运行程序，检查是否恢复展开状态...")
        
        # 第二次运行
        root2 = tk.Tk()
        root2.title("目录树记忆测试 - 第二次运行")
        root2.geometry("1000x600")
        
        app2 = FileSearchApp(root2)
        
        def second_run_test():
            try:
                # 等待状态加载完成
                time.sleep(1)
                root2.update()
                
                # 检查是否恢复了展开状态
                current_expanded = len(app2.expanded_directories)
                print(f"第二次运行时记录了 {current_expanded} 个展开目录")
                
                if current_expanded > 0:
                    print("✓ 展开状态已恢复")
                    
                    # 检查具体的展开状态
                    restored_count = 0
                    
                    def check_expanded_items(parent=""):
                        nonlocal restored_count
                        for item in app2.directory_tree.get_children(parent):
                            if app2.directory_tree.item(item, "open"):
                                restored_count += 1
                                tags = app2.directory_tree.item(item, "tags")
                                if tags:
                                    print(f"已展开: {tags[0]}")
                            check_expanded_items(item)
                    
                    check_expanded_items()
                    
                    print(f"实际展开的目录数: {restored_count}")
                    
                    if restored_count > 0:
                        print("✓ 目录树展开状态恢复正常")
                        return True
                    else:
                        print("❌ 目录树没有实际展开")
                        return False
                else:
                    print("❌ 展开状态未恢复")
                    return False
                    
            except Exception as e:
                print(f"❌ 第二次运行测试失败: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        # 执行第二次测试
        root2.after(1000, lambda: test_and_close(second_run_test, root2, app2))
        root2.mainloop()
        
        print("\n3. 测试状态文件时效性...")
        
        # 修改状态文件的时间戳，模拟过期
        if os.path.exists(state_file):
            with open(state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            # 设置为8天前（超过7天有效期）
            state_data['last_updated'] = time.time() - 8 * 24 * 3600
            
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, ensure_ascii=False, indent=2)
            
            print("已修改状态文件时间戳为8天前")
            
            # 第三次运行，测试过期处理
            root3 = tk.Tk()
            root3.title("目录树记忆测试 - 过期测试")
            root3.geometry("1000x600")
            
            app3 = FileSearchApp(root3)
            
            def third_run_test():
                try:
                    time.sleep(0.5)
                    root3.update()
                    
                    # 检查是否因为过期而没有恢复状态
                    current_expanded = len(app3.expanded_directories)
                    print(f"过期测试中记录了 {current_expanded} 个展开目录")
                    
                    if current_expanded == 0:
                        print("✓ 过期状态文件正确处理（未恢复）")
                        return True
                    else:
                        print("❌ 过期状态文件处理有问题（仍然恢复了）")
                        return False
                        
                except Exception as e:
                    print(f"❌ 过期测试失败: {e}")
                    return False
            
            root3.after(1000, lambda: test_and_close(third_run_test, root3, app3))
            root3.mainloop()
        
        print("\n🎉 目录树记忆功能测试完成！")
        
    finally:
        # 清理测试目录和状态文件
        try:
            shutil.rmtree(test_base_dir)
            print(f"清理测试目录: {test_base_dir}")
        except Exception as e:
            print(f"清理测试目录失败: {e}")
        
        # 清理状态文件
        try:
            if 'app1' in locals():
                state_file = app1.directory_tree_state_file
                if os.path.exists(state_file):
                    os.remove(state_file)
                    print(f"清理状态文件: {state_file}")
        except Exception as e:
            print(f"清理状态文件失败: {e}")


def test_and_close(test_func, root, app):
    """执行测试并关闭窗口"""
    try:
        success = test_func()
        if success:
            print("✓ 当前阶段测试通过")
        else:
            print("❌ 当前阶段测试失败")
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
    finally:
        # 保存状态并关闭
        try:
            app.save_directory_tree_state()
        except:
            pass
        root.quit()
        root.destroy()


def main():
    """主函数"""
    print("=" * 50)
    print("目录树记忆功能测试")
    print("=" * 50)
    
    test_directory_tree_memory()


if __name__ == "__main__":
    main()
