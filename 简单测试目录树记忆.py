"""
简单测试目录树记忆功能
"""
import os
import tempfile
import shutil
import tkinter as tk
import json
import time


def test_memory_functionality():
    """测试记忆功能的基本工作"""
    print("开始简单测试目录树记忆功能...")
    
    try:
        # 导入主程序
        import sys
        sys.path.append('.')
        
        from 重复文件查找器 import FileSearchApp
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("目录树记忆功能简单测试")
        root.geometry("800x600")
        
        # 创建应用实例
        app = FileSearchApp(root)
        
        print("1. 检查记忆功能相关属性...")
        
        # 检查必要的属性是否存在
        required_attrs = [
            'expanded_directories',
            'directory_tree_state_file',
            'save_directory_tree_state',
            'load_directory_tree_state',
            'update_expanded_directories_state'
        ]
        
        missing_attrs = []
        for attr in required_attrs:
            if not hasattr(app, attr):
                missing_attrs.append(attr)
        
        if missing_attrs:
            print(f"❌ 缺少必要属性: {missing_attrs}")
            return False
        else:
            print("✓ 所有必要属性都存在")
        
        print("2. 检查状态文件路径...")
        
        state_file = app.directory_tree_state_file
        print(f"状态文件路径: {state_file}")
        
        if state_file and isinstance(state_file, str):
            print("✓ 状态文件路径设置正确")
        else:
            print("❌ 状态文件路径设置错误")
            return False
        
        print("3. 测试状态保存功能...")
        
        # 模拟一些展开状态
        test_paths = [
            "C:\\Users",
            "C:\\Program Files",
            "D:\\Documents"
        ]
        
        app.expanded_directories = set(test_paths)
        
        try:
            app.save_directory_tree_state()
            print("✓ 状态保存功能调用成功")
            
            # 检查文件是否创建
            if os.path.exists(state_file):
                print("✓ 状态文件创建成功")
                
                # 读取文件内容
                with open(state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)
                
                if 'expanded_directories' in state_data:
                    saved_paths = state_data['expanded_directories']
                    print(f"✓ 保存了 {len(saved_paths)} 个展开目录")
                    
                    if len(saved_paths) > 0:
                        print("✓ 状态保存内容正确")
                    else:
                        print("❌ 状态保存内容为空")
                        return False
                else:
                    print("❌ 状态文件格式错误")
                    return False
            else:
                print("❌ 状态文件未创建")
                return False
                
        except Exception as e:
            print(f"❌ 状态保存功能出错: {e}")
            return False
        
        print("4. 测试状态加载功能...")
        
        # 清空当前状态
        app.expanded_directories = set()
        
        try:
            app.load_directory_tree_state()
            print("✓ 状态加载功能调用成功")
            
            # 检查是否恢复了状态
            if len(app.expanded_directories) > 0:
                print(f"✓ 恢复了 {len(app.expanded_directories)} 个展开目录")
                print("✓ 状态加载功能正常")
            else:
                print("❌ 状态加载后没有恢复展开目录")
                return False
                
        except Exception as e:
            print(f"❌ 状态加载功能出错: {e}")
            return False
        
        print("5. 测试状态更新功能...")
        
        try:
            app.update_expanded_directories_state()
            print("✓ 状态更新功能调用成功")
        except Exception as e:
            print(f"❌ 状态更新功能出错: {e}")
            return False
        
        print("6. 测试目录树组件...")
        
        # 检查目录树是否存在
        if hasattr(app, 'directory_tree') and app.directory_tree:
            print("✓ 目录树组件存在")
            
            # 检查是否绑定了记忆相关的事件
            if hasattr(app, 'on_directory_expand') and hasattr(app, 'on_directory_collapse'):
                print("✓ 记忆相关事件处理方法存在")
            else:
                print("❌ 记忆相关事件处理方法缺失")
                return False
        else:
            print("❌ 目录树组件不存在")
            return False
        
        print("7. 清理测试文件...")
        
        # 清理测试创建的状态文件
        try:
            if os.path.exists(state_file):
                os.remove(state_file)
                print("✓ 测试状态文件已清理")
        except Exception as e:
            print(f"⚠️ 清理测试文件失败: {e}")
        
        print("\n🎉 所有基本功能测试通过！")
        print("✓ 目录树记忆功能已正确实现")
        print("✓ 状态保存和加载功能正常")
        print("✓ 相关组件和方法都存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        try:
            root.quit()
            root.destroy()
        except:
            pass


def main():
    """主函数"""
    print("=" * 50)
    print("目录树记忆功能简单测试")
    print("=" * 50)
    
    success = test_memory_functionality()
    
    if success:
        print("\n✅ 测试结果：目录树记忆功能实现正确！")
        print("\n功能说明：")
        print("• 程序会自动记住你展开的目录")
        print("• 下次启动时会自动恢复展开状态")
        print("• 状态文件保存在用户主目录")
        print("• 状态有7天的有效期")
        print("• 所有操作都是自动的，无需手动干预")
    else:
        print("\n❌ 测试结果：目录树记忆功能存在问题！")
        print("请检查实现代码或查看错误信息")


if __name__ == "__main__":
    main()
