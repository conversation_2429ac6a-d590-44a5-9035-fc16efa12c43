"""
UI管理器模块 - 负责界面创建和更新
"""
import tkinter as tk
from tkinter import ttk
import os
from pathlib import Path


class UIManager:
    """UI管理器 - 专门负责界面创建和更新"""
    
    def __init__(self, root):
        self.root = root
        self.style = ttk.Style()
        self._setup_styles()
        
        # UI组件引用
        self.main_frame = None
        self.text_area = None
        self.preview_label = None
        self.progress_var = None
        self.progress = None
        self.current_file_var = None
        self.path_var = None
        self.status_var = None
        self.preview_filename_var = None
        
        # 按钮引用
        self.buttons = {}
        
        # 勾选框变量
        self.checkbox_vars = {}
        self.delete_buttons = {}
        
    def _setup_styles(self):
        """设置UI样式"""
        self.style.configure("Delete.TButton", padding=2)
        self.style.configure("Highlight.TLabel", background="#e0e0ff")
        self.style.configure("Selected.TLabel", background="#c0c0ff")
        self.style.configure('Accent.TButton', foreground='black', background='#0078D4')
        self.style.configure("Stats.TLabel", font=("微软雅黑", 9))
    
    def create_main_ui(self):
        """创建主界面"""
        # 设置窗口属性
        self.root.title("重复文件查找器")
        self.root.geometry("1400x768")
        self.root.minsize(1200, 600)
        
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建按钮区域
        self._create_button_area()
        
        # 创建路径显示
        self._create_path_display()
        
        # 创建进度条
        self._create_progress_bar()
        
        # 创建主要内容区域
        self._create_content_area()
        
        # 创建状态栏
        self._create_status_bar()
        
        return self._get_ui_components()
    
    def _create_button_area(self):
        """创建按钮区域"""
        button_frame = ttk.Frame(self.main_frame)
        button_frame.pack(fill=tk.X, pady=5)
        
        # 第一行按钮
        button_row1 = ttk.Frame(button_frame)
        button_row1.pack(fill=tk.X, pady=2)
        
        self.buttons['select'] = ttk.Button(button_row1, text="选择文件夹")
        self.buttons['select'].pack(side=tk.LEFT, padx=5)
        
        self.buttons['search_size'] = ttk.Button(button_row1, text="相同大小", state='disabled')
        self.buttons['search_size'].pack(side=tk.LEFT, padx=5)
        
        self.buttons['search_duration'] = ttk.Button(button_row1, text="相同时长", state='disabled')
        self.buttons['search_duration'].pack(side=tk.LEFT, padx=5)
        
        self.buttons['save'] = ttk.Button(button_row1, text="保存结果", state='disabled')
        self.buttons['save'].pack(side=tk.LEFT, padx=5)
        
        self.buttons['load'] = ttk.Button(button_row1, text="加载结果")
        self.buttons['load'].pack(side=tk.LEFT, padx=5)
        
        # 包含图片文件勾选框
        self.include_images_var = tk.BooleanVar(value=False)
        include_images_checkbox = ttk.Checkbutton(button_row1, text="包含图片文件",
                                                 variable=self.include_images_var)
        include_images_checkbox.pack(side=tk.LEFT, padx=5)
        
        # 第二行按钮
        button_row2 = ttk.Frame(button_frame)
        button_row2.pack(fill=tk.X, pady=2)
        
        self.buttons['select_all'] = ttk.Button(button_row2, text="全选")
        self.buttons['select_all'].pack(side=tk.LEFT, padx=5)
        
        self.buttons['deselect_all'] = ttk.Button(button_row2, text="取消全选")
        self.buttons['deselect_all'].pack(side=tk.LEFT, padx=5)
        
        self.buttons['delete_selected'] = ttk.Button(button_row2, text="删除选中")
        self.buttons['delete_selected'].pack(side=tk.LEFT, padx=5)
        
        self.buttons['remove_from_list'] = ttk.Button(button_row2, text="移出列表")
        self.buttons['remove_from_list'].pack(side=tk.LEFT, padx=5)
        
        # 选择选项勾选框
        self.same_dir_var = tk.BooleanVar()
        same_dir_checkbox = ttk.Checkbutton(button_row2, text="同目录选择",
                                           variable=self.same_dir_var)
        same_dir_checkbox.pack(side=tk.LEFT, padx=5)
        
        self.same_time_var = tk.BooleanVar()
        same_time_checkbox = ttk.Checkbutton(button_row2, text="相同时段",
                                            variable=self.same_time_var)
        same_time_checkbox.pack(side=tk.LEFT, padx=5)
    
    def _create_path_display(self):
        """创建路径显示"""
        self.path_var = tk.StringVar()
        path_label = ttk.Label(self.main_frame, textvariable=self.path_var)
        path_label.pack(fill=tk.X, pady=5)
    
    def _create_progress_bar(self):
        """创建进度条"""
        self.progress_var = tk.DoubleVar()
        self.progress = ttk.Progressbar(self.main_frame, length=300, mode='determinate', 
                                       variable=self.progress_var)
        self.progress.pack(fill=tk.X, pady=5)
        
        self.current_file_var = tk.StringVar()
        current_file_label = ttk.Label(self.main_frame, textvariable=self.current_file_var)
        current_file_label.pack(fill=tk.X, pady=5)
    
    def _create_content_area(self):
        """创建主要内容区域"""
        # 创建水平分隔框架
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 左侧结果显示区域
        result_frame = ttk.LabelFrame(paned_window, text="搜索结果")
        paned_window.add(result_frame, weight=1)
        
        # 创建文本框和滚动条的容器
        text_container = ttk.Frame(result_frame)
        text_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建文本框
        self.text_area = tk.Text(text_container, wrap=tk.WORD, font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(text_container, orient="vertical", command=self.text_area.yview)
        self.text_area.configure(yscrollcommand=scrollbar.set)
        self.text_area.configure(tabs=('500p', '580p'))
        
        self.text_area.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 右侧预览区域
        preview_frame = ttk.LabelFrame(paned_window, text="预览")
        paned_window.add(preview_frame, weight=1)
        
        # 预览控制按钮
        preview_control_frame = ttk.Frame(preview_frame)
        preview_control_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
        
        self.buttons['stop_preview'] = ttk.Button(preview_control_frame, text="停止预览", state='disabled')
        self.buttons['stop_preview'].pack(side=tk.LEFT, padx=5)
        
        self.preview_filename_var = tk.StringVar()
        preview_filename_label = ttk.Label(preview_control_frame,
                                          textvariable=self.preview_filename_var,
                                          font=('微软雅黑', 9))
        preview_filename_label.pack(side=tk.LEFT, padx=10, fill=tk.X, expand=True)
        
        # 预览标签容器
        preview_container = ttk.Frame(preview_frame)
        preview_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.preview_label = ttk.Label(preview_container, text="选择文件进行预览")
        self.preview_label.pack(expand=True, fill=tk.BOTH)
        
        # 选中文件显示区域
        selected_files_frame = ttk.LabelFrame(self.main_frame, text="已选中文件 (0 个文件，0 字节)")
        selected_files_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        selected_tree = ttk.Treeview(selected_files_frame, selectmode='browse')
        selected_tree.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)
        
        selected_tree_scroll = ttk.Scrollbar(selected_files_frame, orient="vertical", 
                                           command=selected_tree.yview)
        selected_tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        selected_tree.configure(yscrollcommand=selected_tree_scroll.set)
        
        # 配置树形视图
        selected_tree["columns"] = ("size",)
        selected_tree.column("#0", width=400, stretch=tk.YES)
        selected_tree.column("size", width=100, anchor=tk.E)
        selected_tree.heading("#0", text="文件路径")
        selected_tree.heading("size", text="大小")
        
        self.selected_tree = selected_tree
        self.selected_files_frame = selected_files_frame
    
    def _create_status_bar(self):
        """创建状态栏"""
        # 分隔线
        separator = ttk.Separator(self.root, orient='horizontal')
        separator.pack(fill=tk.X, side=tk.BOTTOM, padx=5)
        
        # 状态栏框架
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=10, pady=5)
        
        # 左侧状态显示
        self.status_var = tk.StringVar()
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.pack(side=tk.LEFT, padx=5)
        
        # 右侧统计信息
        stats_label = ttk.Label(status_frame, text="已选择: 0 个文件，总大小: 0 字节")
        stats_label.configure(style="Stats.TLabel")
        stats_label.pack(side=tk.RIGHT, padx=5)
        
        self.stats_label = stats_label
    
    def _get_ui_components(self):
        """获取UI组件字典"""
        return {
            'main_frame': self.main_frame,
            'text_area': self.text_area,
            'preview_label': self.preview_label,
            'progress_var': self.progress_var,
            'progress': self.progress,
            'current_file_var': self.current_file_var,
            'path_var': self.path_var,
            'status_var': self.status_var,
            'preview_filename_var': self.preview_filename_var,
            'buttons': self.buttons,
            'selected_tree': self.selected_tree,
            'selected_files_frame': self.selected_files_frame,
            'stats_label': self.stats_label,
            'include_images_var': self.include_images_var,
            'same_dir_var': self.same_dir_var,
            'same_time_var': self.same_time_var
        }
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_var.set(value)
        self.root.update_idletasks()
    
    def update_status(self, message):
        """更新状态信息"""
        self.status_var.set(message)
        self.root.update_idletasks()
    
    def update_current_file(self, filename):
        """更新当前处理文件显示"""
        self.current_file_var.set(filename)
        self.root.update_idletasks()
    
    def update_path_display(self, path):
        """更新路径显示"""
        self.path_var.set(f"当前目录: {path}")
    
    def clear_results(self):
        """清空结果显示"""
        self.text_area.delete(1.0, tk.END)
        self.checkbox_vars.clear()
        self.delete_buttons.clear()
    
    def enable_search_buttons(self):
        """启用搜索按钮"""
        self.buttons['search_size'].configure(state='normal')
        self.buttons['search_duration'].configure(state='normal')
    
    def disable_search_buttons(self):
        """禁用搜索按钮"""
        self.buttons['search_size'].configure(state='disabled')
        self.buttons['search_duration'].configure(state='disabled')
    
    def enable_save_button(self):
        """启用保存按钮"""
        self.buttons['save'].configure(state='normal')
    
    def disable_save_button(self):
        """禁用保存按钮"""
        self.buttons['save'].configure(state='disabled')
    
    def get_relative_path(self, file_path, base_directory):
        """获取相对路径"""
        try:
            return os.path.relpath(file_path, base_directory)
        except ValueError:
            return file_path
    
    def format_file_size(self, size):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"
    
    def format_duration(self, seconds):
        """格式化时长"""
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
