"""
测试目录树选择功能 - 验证选择目录与选择文件夹按钮效果一致
"""
import os
import tempfile
import shutil
import tkinter as tk
import time


def create_test_directory_structure():
    """创建测试目录结构"""
    base_dir = tempfile.mkdtemp(prefix="tree_select_test_")
    print(f"创建测试基础目录: {base_dir}")
    
    # 创建多层目录结构
    test_structure = {
        "测试目录A": {
            "子目录1": ["file1.txt", "file2.txt"],
            "子目录2": ["file3.txt", "file4.txt"]
        },
        "测试目录B": {
            "资源": ["image1.jpg", "image2.png"],
            "文档": ["doc1.pdf", "doc2.docx"]
        },
        "测试目录C": ["readme.txt", "config.ini"]
    }
    
    def create_structure(base_path, structure):
        for name, content in structure.items():
            dir_path = os.path.join(base_path, name)
            os.makedirs(dir_path, exist_ok=True)
            
            if isinstance(content, dict):
                create_structure(dir_path, content)
            elif isinstance(content, list):
                for filename in content:
                    file_path = os.path.join(dir_path, filename)
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(f"测试文件内容: {filename}")
    
    create_structure(base_dir, test_structure)
    
    # 返回一些测试路径
    test_paths = {
        'base': base_dir,
        'dir_a': os.path.join(base_dir, "测试目录A"),
        'dir_b': os.path.join(base_dir, "测试目录B"),
        'dir_c': os.path.join(base_dir, "测试目录C"),
        'subdir1': os.path.join(base_dir, "测试目录A", "子目录1"),
        'subdir2': os.path.join(base_dir, "测试目录A", "子目录2")
    }
    
    return base_dir, test_paths


def test_directory_tree_selection():
    """测试目录树选择功能"""
    print("开始测试目录树选择功能...")
    
    # 创建测试目录结构
    test_base_dir, test_paths = create_test_directory_structure()
    
    try:
        # 导入主程序
        import sys
        sys.path.append('.')
        
        # 创建主窗口
        root = tk.Tk()
        root.title("目录树选择功能测试")
        root.geometry("1200x800")
        
        # 导入并创建应用实例
        from 重复文件查找器 import FileSearchApp
        app = FileSearchApp(root)
        
        print("1. 检查初始状态...")
        
        # 检查初始状态
        initial_current_dir = app.current_directory
        initial_path_text = app.path_var.get()
        initial_search_btn_state = app.search_by_size_btn.cget('state')
        
        print(f"初始当前目录: {initial_current_dir}")
        print(f"初始路径显示: {initial_path_text}")
        print(f"初始搜索按钮状态: {initial_search_btn_state}")
        
        # 模拟测试过程
        def simulate_test():
            try:
                print("2. 展开到测试目录...")
                
                # 展开到测试基础目录
                app.expand_to_directory(test_base_dir)
                root.update()
                time.sleep(0.2)
                
                # 检查是否正确展开和选择
                if app.current_directory != test_base_dir:
                    print(f"❌ 展开后当前目录不正确: {app.current_directory} != {test_base_dir}")
                    return False
                else:
                    print("✓ 展开后当前目录设置正确")
                
                # 检查路径显示
                path_text = app.path_var.get()
                if test_base_dir not in path_text:
                    print(f"❌ 路径显示不正确: {path_text}")
                    return False
                else:
                    print("✓ 路径显示正确")
                
                # 检查搜索按钮状态
                search_btn_state = app.search_by_size_btn.cget('state')
                if search_btn_state != 'normal':
                    print(f"❌ 搜索按钮状态不正确: {search_btn_state}")
                    return False
                else:
                    print("✓ 搜索按钮已启用")
                
                print("3. 测试选择子目录...")
                
                # 选择一个子目录
                test_subdir = test_paths['dir_a']
                print(f"选择子目录: {test_subdir}")
                
                # 模拟目录树选择
                app.expand_to_directory(test_subdir)
                root.update()
                time.sleep(0.2)
                
                # 检查当前目录是否更新
                if app.current_directory != test_subdir:
                    print(f"❌ 选择子目录后当前目录不正确: {app.current_directory} != {test_subdir}")
                    return False
                else:
                    print("✓ 选择子目录后当前目录设置正确")
                
                # 检查界面状态
                path_text = app.path_var.get()
                if test_subdir not in path_text:
                    print(f"❌ 子目录路径显示不正确: {path_text}")
                    return False
                else:
                    print("✓ 子目录路径显示正确")
                
                print("4. 测试与选择文件夹按钮的一致性...")
                
                # 记录通过目录树选择的状态
                tree_selected_dir = app.current_directory
                tree_path_text = app.path_var.get()
                tree_search_btn_state = app.search_by_size_btn.cget('state')
                tree_text_content = app.text_area.get(1.0, tk.END).strip()
                
                print(f"目录树选择状态:")
                print(f"  当前目录: {tree_selected_dir}")
                print(f"  路径显示: {tree_path_text}")
                print(f"  搜索按钮: {tree_search_btn_state}")
                print(f"  文本内容: {tree_text_content[:50]}...")
                
                # 重置状态
                app.current_directory = ""
                app.path_var.set("")
                app.search_by_size_btn.configure(state='disabled')
                app.search_by_duration_btn.configure(state='disabled')
                app.text_area.delete(1.0, tk.END)
                root.update()
                
                print("5. 使用选择文件夹按钮选择相同目录...")
                
                # 模拟选择文件夹按钮的行为
                app.current_directory = test_subdir
                app.path_var.set(f"当前文件夹: {test_subdir}")
                app.search_by_size_btn.configure(state='normal')
                app.search_by_duration_btn.configure(state='normal')
                app.current_results = {}
                app.checkbox_vars = {}
                app.delete_buttons = {}
                app.hide_sort_buttons()
                app.update_selected_files_tree()
                app.text_area.delete(1.0, tk.END)
                app.text_area.insert(tk.END, "请选择搜索方式：相同大小 或 相同时长\n")
                app.progress_var.set(0)
                app.current_file_var.set("已选择文件夹，请选择搜索方式")
                root.update()
                
                # 记录通过按钮选择的状态
                button_selected_dir = app.current_directory
                button_path_text = app.path_var.get()
                button_search_btn_state = app.search_by_size_btn.cget('state')
                button_text_content = app.text_area.get(1.0, tk.END).strip()
                
                print(f"按钮选择状态:")
                print(f"  当前目录: {button_selected_dir}")
                print(f"  路径显示: {button_path_text}")
                print(f"  搜索按钮: {button_search_btn_state}")
                print(f"  文本内容: {button_text_content[:50]}...")
                
                print("6. 对比两种方式的效果...")
                
                # 对比结果
                results = []
                
                # 检查当前目录
                if tree_selected_dir == button_selected_dir:
                    print("✓ 当前目录设置一致")
                    results.append(True)
                else:
                    print(f"❌ 当前目录设置不一致: {tree_selected_dir} != {button_selected_dir}")
                    results.append(False)
                
                # 检查搜索按钮状态
                if tree_search_btn_state == button_search_btn_state:
                    print("✓ 搜索按钮状态一致")
                    results.append(True)
                else:
                    print(f"❌ 搜索按钮状态不一致: {tree_search_btn_state} != {button_search_btn_state}")
                    results.append(False)
                
                # 检查文本内容
                if tree_text_content == button_text_content:
                    print("✓ 界面文本内容一致")
                    results.append(True)
                else:
                    print(f"❌ 界面文本内容不一致")
                    print(f"  目录树: {tree_text_content}")
                    print(f"  按钮: {button_text_content}")
                    results.append(False)
                
                print("7. 测试双击展开/折叠功能...")
                
                # 重新展开到测试目录
                app.expand_to_directory(test_base_dir)
                root.update()
                
                # 找到测试目录A的树项
                def find_tree_item_by_path(target_path):
                    def search_items(parent=""):
                        for item in app.directory_tree.get_children(parent):
                            tags = app.directory_tree.item(item, "tags")
                            if tags and tags[0] == target_path:
                                return item
                            # 递归搜索子项
                            result = search_items(item)
                            if result:
                                return result
                        return None
                    return search_items()
                
                dir_a_item = find_tree_item_by_path(test_paths['dir_a'])
                if dir_a_item:
                    # 检查初始展开状态
                    initial_open = app.directory_tree.item(dir_a_item, "open")
                    print(f"目录A初始展开状态: {initial_open}")
                    
                    # 模拟双击
                    app.directory_tree.selection_set(dir_a_item)
                    
                    class MockEvent:
                        pass
                    
                    app.on_directory_double_click(MockEvent())
                    root.update()
                    
                    # 检查展开状态是否改变
                    after_double_click = app.directory_tree.item(dir_a_item, "open")
                    print(f"双击后展开状态: {after_double_click}")
                    
                    if initial_open != after_double_click:
                        print("✓ 双击展开/折叠功能正常")
                        results.append(True)
                    else:
                        print("❌ 双击展开/折叠功能异常")
                        results.append(False)
                else:
                    print("❌ 找不到测试目录A的树项")
                    results.append(False)
                
                # 总结测试结果
                if all(results):
                    print("8. 所有测试通过！")
                    return True
                else:
                    print(f"8. 测试失败！{sum(results)}/{len(results)} 项通过")
                    return False
                
            except Exception as e:
                print(f"❌ 测试过程中出错: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        # 在主线程中执行测试
        root.after(500, lambda: test_and_exit(simulate_test, root))
        
        # 运行主循环
        root.mainloop()
        
    finally:
        # 清理测试目录
        try:
            shutil.rmtree(test_base_dir)
            print(f"清理测试目录: {test_base_dir}")
        except Exception as e:
            print(f"清理测试目录失败: {e}")


def test_and_exit(test_func, root):
    """执行测试并退出"""
    try:
        success = test_func()
        if success:
            print("\n🎉 所有测试通过！目录树选择功能与选择文件夹按钮效果一致。")
            print("✓ 单击目录树项目自动设置为当前工作目录")
            print("✓ 界面状态更新与选择文件夹按钮一致")
            print("✓ 双击展开/折叠功能正常")
        else:
            print("\n❌ 测试失败！需要进一步检查实现。")
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
    finally:
        # 延迟关闭，让用户看到结果
        root.after(3000, root.quit)


def main():
    """主函数"""
    print("=" * 50)
    print("目录树选择功能测试")
    print("=" * 50)
    
    test_directory_tree_selection()


if __name__ == "__main__":
    main()
