"""
相同大小选择功能演示
创建实际的重复文件并演示相同大小选择功能
"""
import os
import tempfile
import shutil
import tkinter as tk
import time


def create_realistic_duplicate_files():
    """创建真实的重复文件场景"""
    base_dir = tempfile.mkdtemp(prefix="duplicate_demo_")
    print(f"创建演示目录: {base_dir}")
    
    # 场景1: 同一图片的不同分辨率版本
    images_dir = os.path.join(base_dir, "图片")
    os.makedirs(images_dir, exist_ok=True)
    
    # 模拟同一张图片的不同版本（通过文件大小模拟不同分辨率）
    image_contents = {
        "photo1_thumbnail.jpg": "JPEG_THUMBNAIL_DATA" * 10,      # 小缩略图
        "photo1_medium.jpg": "JPEG_MEDIUM_QUALITY_DATA" * 50,   # 中等质量
        "photo1_high.jpg": "JPEG_HIGH_QUALITY_DATA" * 200,      # 高质量
        "photo1_original.jpg": "JPEG_ORIGINAL_DATA" * 500,      # 原始文件
    }
    
    for filename, content in image_contents.items():
        file_path = os.path.join(images_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"创建图片文件: {filename} ({len(content)} 字节)")
    
    # 场景2: 同一文档的不同版本
    docs_dir = os.path.join(base_dir, "文档")
    os.makedirs(docs_dir, exist_ok=True)
    
    doc_contents = {
        "report_summary.txt": "报告摘要内容" * 20,              # 摘要版本
        "report_full.txt": "完整报告内容包含详细数据" * 100,      # 完整版本
        "report_extended.txt": "扩展报告包含所有附录和详细分析" * 300,  # 扩展版本
    }
    
    for filename, content in doc_contents.items():
        file_path = os.path.join(docs_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"创建文档文件: {filename} ({len(content)} 字节)")
    
    # 场景3: 同一视频的不同质量版本
    videos_dir = os.path.join(base_dir, "视频")
    os.makedirs(videos_dir, exist_ok=True)
    
    video_contents = {
        "movie_480p.mp4": "VIDEO_480P_DATA" * 100,      # 标清版本
        "movie_720p.mp4": "VIDEO_720P_DATA" * 300,      # 高清版本
        "movie_1080p.mp4": "VIDEO_1080P_DATA" * 800,    # 全高清版本
        "movie_4k.mp4": "VIDEO_4K_DATA" * 2000,         # 4K版本
    }
    
    for filename, content in video_contents.items():
        file_path = os.path.join(videos_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"创建视频文件: {filename} ({len(content)} 字节)")
    
    return base_dir


def demonstrate_same_size_selection():
    """演示相同大小选择功能"""
    print("=" * 60)
    print("相同大小选择功能演示")
    print("=" * 60)
    
    # 创建演示文件
    demo_dir = create_realistic_duplicate_files()
    
    try:
        # 导入主程序
        import sys
        sys.path.append('.')
        
        # 创建主窗口
        root = tk.Tk()
        root.title("相同大小选择功能演示")
        root.geometry("1400x900")
        
        # 导入并创建应用实例
        from 重复文件查找器 import FileSearchApp
        app = FileSearchApp(root)
        
        print("\n1. 设置演示目录...")
        
        # 设置当前目录
        app.current_directory = demo_dir
        app.path_var.set(f"当前文件夹: {demo_dir}")
        app.search_by_size_btn.configure(state='normal')
        app.search_by_duration_btn.configure(state='normal')
        
        print(f"当前目录: {demo_dir}")
        
        def run_demonstration():
            try:
                print("\n2. 模拟搜索重复文件...")
                
                # 收集所有文件
                all_files = []
                for root_path, dirs, files in os.walk(demo_dir):
                    for file in files:
                        file_path = os.path.join(root_path, file)
                        all_files.append(file_path)
                
                print(f"找到 {len(all_files)} 个文件")
                
                # 按文件大小分组（模拟相同大小搜索结果）
                size_groups = {}
                for file_path in all_files:
                    if os.path.exists(file_path):
                        file_size = os.path.getsize(file_path)
                        if file_size not in size_groups:
                            size_groups[file_size] = []
                        size_groups[file_size].append(file_path)
                
                # 只保留有多个文件的组（模拟重复文件）
                duplicate_groups = {}
                for size, files in size_groups.items():
                    if len(files) > 1:
                        duplicate_groups[size] = files
                
                print(f"发现 {len(duplicate_groups)} 个重复文件组")
                
                # 显示每个组的详细信息
                for i, (size, files) in enumerate(duplicate_groups.items(), 1):
                    print(f"\n组 {i} (大小: {size} 字节):")
                    for file_path in files:
                        filename = os.path.basename(file_path)
                        print(f"  - {filename}")
                
                # 设置搜索结果
                app.current_results = duplicate_groups
                
                # 创建勾选框变量
                app.checkbox_vars = {}
                for size, files in duplicate_groups.items():
                    for file_path in files:
                        app.checkbox_vars[file_path] = tk.BooleanVar()
                
                print("\n3. 演示相同大小选择功能...")
                
                # 启用相同大小选择
                app.same_size_var.set(True)
                print("✓ 启用相同大小选择")
                
                # 演示偏大选择
                print("\n--- 演示偏大选择 ---")
                app.size_preference_var.set("larger")
                
                # 选择第一个组中的一个文件来触发选择
                if duplicate_groups:
                    first_group = list(duplicate_groups.values())[0]
                    test_file = first_group[0]
                    test_var = app.checkbox_vars[test_file]
                    
                    print(f"触发文件: {os.path.basename(test_file)}")
                    
                    # 模拟勾选框点击
                    test_var.set(True)
                    app.on_checkbox_click(test_file, test_var)
                    root.update()
                    
                    # 显示选择结果
                    print("偏大选择结果:")
                    for size, files in duplicate_groups.items():
                        selected_files = []
                        for file_path in files:
                            if file_path in app.checkbox_vars and app.checkbox_vars[file_path].get():
                                selected_files.append(os.path.basename(file_path))
                        
                        if selected_files:
                            print(f"  组 (大小: {size} 字节): {', '.join(selected_files)}")
                
                # 清空选择
                for var in app.checkbox_vars.values():
                    var.set(False)
                
                # 演示偏小选择
                print("\n--- 演示偏小选择 ---")
                app.size_preference_var.set("smaller")
                
                if duplicate_groups:
                    first_group = list(duplicate_groups.values())[0]
                    test_file = first_group[0]
                    test_var = app.checkbox_vars[test_file]
                    
                    print(f"触发文件: {os.path.basename(test_file)}")
                    
                    # 模拟勾选框点击
                    test_var.set(True)
                    app.on_checkbox_click(test_file, test_var)
                    root.update()
                    
                    # 显示选择结果
                    print("偏小选择结果:")
                    for size, files in duplicate_groups.items():
                        selected_files = []
                        for file_path in files:
                            if file_path in app.checkbox_vars and app.checkbox_vars[file_path].get():
                                selected_files.append(os.path.basename(file_path))
                        
                        if selected_files:
                            print(f"  组 (大小: {size} 字节): {', '.join(selected_files)}")
                
                print("\n4. 演示组合选择功能...")
                
                # 清空选择
                for var in app.checkbox_vars.values():
                    var.set(False)
                
                # 演示同目录 + 相同大小选择
                print("\n--- 演示同目录 + 相同大小选择 ---")
                app.same_dir_var.set(True)
                app.same_size_var.set(True)
                app.size_preference_var.set("larger")
                
                if duplicate_groups:
                    first_group = list(duplicate_groups.values())[0]
                    test_file = first_group[0]
                    test_var = app.checkbox_vars[test_file]
                    
                    print(f"触发文件: {os.path.basename(test_file)}")
                    print(f"文件目录: {os.path.dirname(test_file)}")
                    
                    # 模拟勾选框点击
                    test_var.set(True)
                    app.on_checkbox_click(test_file, test_var)
                    root.update()
                    
                    # 显示选择结果
                    print("同目录+偏大选择结果:")
                    for size, files in duplicate_groups.items():
                        selected_files = []
                        for file_path in files:
                            if file_path in app.checkbox_vars and app.checkbox_vars[file_path].get():
                                selected_files.append(f"{os.path.basename(file_path)} ({os.path.basename(os.path.dirname(file_path))})")
                        
                        if selected_files:
                            print(f"  组 (大小: {size} 字节): {', '.join(selected_files)}")
                
                print("\n5. 演示完成！")
                print("=" * 60)
                print("功能特点总结:")
                print("✓ 支持偏大/偏小文件选择")
                print("✓ 支持与其他选择条件组合")
                print("✓ 基于实际文件大小进行精确比较")
                print("✓ 智能的中点分割算法")
                print("✓ 友好的状态反馈")
                print("=" * 60)
                
                return True
                
            except Exception as e:
                print(f"❌ 演示过程中出错: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        # 在主线程中执行演示
        root.after(1000, lambda: demo_and_exit(run_demonstration, root))
        
        # 运行主循环
        root.mainloop()
        
    finally:
        # 清理演示目录
        try:
            shutil.rmtree(demo_dir)
            print(f"\n清理演示目录: {demo_dir}")
        except Exception as e:
            print(f"清理演示目录失败: {e}")


def demo_and_exit(demo_func, root):
    """执行演示并退出"""
    try:
        success = demo_func()
        if success:
            print("\n🎉 相同大小选择功能演示成功完成！")
        else:
            print("\n❌ 演示过程中出现问题。")
    except Exception as e:
        print(f"\n❌ 演示执行失败: {e}")
    finally:
        # 延迟关闭，让用户看到结果
        root.after(5000, root.quit)


def main():
    """主函数"""
    demonstrate_same_size_selection()


if __name__ == "__main__":
    main()
