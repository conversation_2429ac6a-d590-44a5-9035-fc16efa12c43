"""
测试排序和滚动位置修复的脚本
"""
import os
import tempfile
import shutil
import tkinter as tk
from tkinter import messagebox
import time


def create_test_files():
    """创建测试文件"""
    test_dir = tempfile.mkdtemp(prefix="sort_test_")
    print(f"创建测试目录: {test_dir}")
    
    # 创建不同大小的文件组
    file_groups = [
        (1024, 5),      # 1KB文件，5个
        (2048, 4),      # 2KB文件，4个  
        (4096, 3),      # 4KB文件，3个
        (8192, 6),      # 8KB文件，6个
        (16384, 2),     # 16KB文件，2个
    ]
    
    created_files = []
    
    for size, count in file_groups:
        for i in range(count):
            filename = f"test_{size}_{i:02d}.dat"
            filepath = os.path.join(test_dir, filename)
            
            with open(filepath, 'wb') as f:
                f.write(b'0' * size)
            
            created_files.append(filepath)
    
    print(f"创建了 {len(created_files)} 个测试文件")
    return test_dir, created_files


def test_sorting_and_scrolling():
    """测试排序和滚动位置修复"""
    print("开始测试排序和滚动位置修复...")
    
    # 创建测试文件
    test_dir, test_files = create_test_files()
    
    try:
        # 导入主程序
        import sys
        sys.path.append('.')
        
        # 创建主窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 导入并创建应用实例
        from 重复文件查找器 import FileSearchApp
        app = FileSearchApp(root)
        
        # 设置测试目录
        app.current_directory = test_dir
        app.path_var.set(f"当前目录: {test_dir}")
        app.search_by_size_btn.configure(state='normal')
        app.search_by_duration_btn.configure(state='normal')
        
        print("1. 执行按大小搜索...")
        
        # 模拟搜索过程
        def simulate_search():
            try:
                # 执行搜索
                app._search_by_size_worker()
                
                print("2. 搜索完成，检查结果...")
                
                # 检查是否有结果
                if not app.current_results:
                    print("❌ 搜索没有找到重复文件")
                    return False
                
                print(f"✓ 找到 {len(app.current_results)} 组重复文件")
                
                # 检查排序缓存
                if app._sorted_results_cache is None:
                    print("❌ 排序缓存未初始化")
                    return False
                
                print("✓ 排序缓存已初始化")
                
                print("3. 测试按时长排序...")
                
                # 切换到时长排序
                app.sort_results_by_duration(app.current_results)
                
                # 检查排序是否生效
                if app._cache_search_type != True:  # True表示时长搜索
                    print("❌ 时长排序缓存状态错误")
                    return False
                
                print("✓ 时长排序缓存状态正确")
                
                print("4. 测试删除文件后排序保持...")
                
                # 获取第一个文件进行删除测试
                first_group = list(app.current_results.values())[0]
                if len(first_group) > 1:
                    test_file = first_group[0]
                    
                    # 记录删除前的排序状态
                    before_cache_type = app._cache_search_type
                    before_cache_size = len(app._sorted_results_cache) if app._sorted_results_cache else 0
                    
                    print(f"删除测试文件: {os.path.basename(test_file)}")
                    
                    # 模拟删除文件
                    try:
                        os.remove(test_file)
                        
                        # 更新内存中的数据
                        for size, files in app.current_results.items():
                            if test_file in files:
                                files.remove(test_file)
                                if len(files) < 2:
                                    del app.current_results[size]
                                break
                        
                        # 重新显示结果
                        app.display_results(app.current_results, preserve_scroll=True)
                        
                        # 检查排序状态是否保持
                        after_cache_type = app._cache_search_type
                        
                        if after_cache_type != before_cache_type:
                            print("❌ 删除文件后排序状态未保持")
                            return False
                        
                        print("✓ 删除文件后排序状态保持正确")
                        
                    except Exception as e:
                        print(f"❌ 删除文件测试失败: {e}")
                        return False
                
                print("5. 测试完全移除组后排序保持...")
                
                # 找一个有多个文件的组进行完全删除测试
                target_group = None
                target_size = None
                
                for size, files in app.current_results.items():
                    if len(files) >= 2:
                        target_group = files.copy()
                        target_size = size
                        break
                
                if target_group:
                    before_cache_type = app._cache_search_type
                    
                    print(f"完全删除组 (大小: {target_size}, 文件数: {len(target_group)})")
                    
                    # 删除组中的所有文件
                    for file_path in target_group:
                        try:
                            if os.path.exists(file_path):
                                os.remove(file_path)
                        except:
                            pass
                    
                    # 从结果中移除该组
                    if target_size in app.current_results:
                        del app.current_results[target_size]
                    
                    # 重新显示结果
                    app.display_results(app.current_results, preserve_scroll=True)
                    
                    # 检查排序状态
                    after_cache_type = app._cache_search_type
                    
                    if after_cache_type != before_cache_type:
                        print("❌ 完全移除组后排序状态未保持")
                        return False
                    
                    print("✓ 完全移除组后排序状态保持正确")
                
                print("6. 所有测试通过！")
                return True
                
            except Exception as e:
                print(f"❌ 测试过程中出错: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        # 在主线程中执行测试
        root.after(100, lambda: test_and_exit(simulate_search, root))
        
        # 运行主循环
        root.mainloop()
        
    finally:
        # 清理测试文件
        try:
            shutil.rmtree(test_dir)
            print(f"清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理测试目录失败: {e}")


def test_and_exit(test_func, root):
    """执行测试并退出"""
    try:
        success = test_func()
        if success:
            print("\n🎉 所有测试通过！排序和滚动位置修复正常工作。")
        else:
            print("\n❌ 测试失败！需要进一步检查修复。")
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
    finally:
        root.quit()


def main():
    """主函数"""
    print("=" * 50)
    print("排序和滚动位置修复测试")
    print("=" * 50)
    
    test_sorting_and_scrolling()


if __name__ == "__main__":
    main()
